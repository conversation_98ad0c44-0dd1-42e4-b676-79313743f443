<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome扩展调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007cba;
        }
        .error {
            background: #ffe6e6;
            border-left-color: #d00;
        }
        .success {
            background: #e6ffe6;
            border-left-color: #080;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .log-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .test-content {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Chrome扩展调试工具</h1>
    
    <div class="debug-section">
        <h2>扩展状态检查</h2>
        <button onclick="checkExtensionStatus()">检查扩展状态</button>
        <button onclick="testPing()">测试Ping</button>
        <button onclick="clearLog()">清空日志</button>
        <div id="statusOutput" class="log-output"></div>
    </div>
    
    <div class="debug-section">
        <h2>API配置测试</h2>
        <button onclick="testAPIConfig()">测试API配置</button>
        <button onclick="testDirectAPI()">直接测试API</button>
        <div id="apiOutput" class="log-output"></div>
    </div>
    
    <div class="debug-section">
        <h2>Markdown提取测试</h2>
        <button onclick="testMarkdownExtraction()">测试Markdown提取</button>
        <button onclick="testLargeContent()">测试大内容</button>
        <div id="markdownOutput" class="log-output"></div>
        
        <div class="test-content">
            <h3>测试内容</h3>
            <p>这是一个用于测试的示例文章内容。包含了多种HTML元素和结构。</p>
            <ul>
                <li>列表项目1</li>
                <li>列表项目2</li>
                <li>列表项目3</li>
            </ul>
            <blockquote>这是一个引用块的示例内容。</blockquote>
        </div>
    </div>
    
    <div class="debug-section">
        <h2>错误诊断</h2>
        <button onclick="diagnoseErrors()">诊断常见错误</button>
        <button onclick="checkPermissions()">检查权限</button>
        <div id="errorOutput" class="log-output"></div>
    </div>

    <script>
        let logCount = 0;
        
        function log(message, outputId = 'statusOutput', type = 'info') {
            const output = document.getElementById(outputId);
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
            logCount++;
        }
        
        function clearLog() {
            document.querySelectorAll('.log-output').forEach(output => {
                output.textContent = '';
            });
            logCount = 0;
        }
        
        function checkExtensionStatus() {
            log('开始检查扩展状态...', 'statusOutput');
            
            if (typeof chrome === 'undefined') {
                log('Chrome API不可用', 'statusOutput', 'error');
                return;
            }
            
            log('Chrome API可用', 'statusOutput', 'success');
            
            if (!chrome.runtime) {
                log('chrome.runtime不可用', 'statusOutput', 'error');
                return;
            }
            
            log(`扩展ID: ${chrome.runtime.id}`, 'statusOutput', 'success');
            
            // 检查各种API
            const apis = [
                'storage', 'tabs', 'scripting', 'permissions', 'sidePanel'
            ];
            
            apis.forEach(api => {
                if (chrome[api]) {
                    log(`${api} API: 可用`, 'statusOutput', 'success');
                } else {
                    log(`${api} API: 不可用`, 'statusOutput', 'warning');
                }
            });
        }
        
        function testPing() {
            log('发送Ping消息...', 'statusOutput');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('Chrome扩展API不可用', 'statusOutput', 'error');
                return;
            }
            
            chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                if (chrome.runtime.lastError) {
                    log(`Ping失败: ${chrome.runtime.lastError.message}`, 'statusOutput', 'error');
                } else {
                    log(`Ping成功: ${JSON.stringify(response)}`, 'statusOutput', 'success');
                }
            });
        }
        
        function testAPIConfig() {
            log('测试API配置...', 'apiOutput');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('Chrome扩展API不可用', 'apiOutput', 'error');
                return;
            }
            
            chrome.runtime.sendMessage({action: 'testAPIConfig'}, (response) => {
                if (chrome.runtime.lastError) {
                    log(`API配置测试失败: ${chrome.runtime.lastError.message}`, 'apiOutput', 'error');
                } else if (response.error) {
                    log(`API配置错误: ${response.error}`, 'apiOutput', 'error');
                } else {
                    log(`API配置测试成功: ${JSON.stringify(response)}`, 'apiOutput', 'success');
                }
            });
        }
        
        async function testDirectAPI() {
            log('直接测试API调用...', 'apiOutput');
            
            try {
                const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer sk-466900693bb54313bb9c9a5feb986eb4'
                    },
                    body: JSON.stringify({
                        model: 'qwen-plus',
                        messages: [{ role: 'user', content: '测试' }],
                        max_tokens: 10
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`直接API调用成功: ${JSON.stringify(data, null, 2)}`, 'apiOutput', 'success');
                } else {
                    const errorText = await response.text();
                    log(`直接API调用失败: ${response.status} ${errorText}`, 'apiOutput', 'error');
                }
            } catch (error) {
                log(`直接API调用异常: ${error.message}`, 'apiOutput', 'error');
            }
        }
        
        function testMarkdownExtraction() {
            log('测试Markdown提取...', 'markdownOutput');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('Chrome扩展API不可用', 'markdownOutput', 'error');
                return;
            }
            
            const testContent = document.querySelector('.test-content').innerText;
            
            log(`测试内容长度: ${testContent.length} 字符`, 'markdownOutput');
            
            chrome.runtime.sendMessage({
                action: 'extractMarkdown',
                content: testContent,
                title: '测试页面',
                url: window.location.href
            }, (response) => {
                if (chrome.runtime.lastError) {
                    log(`Markdown提取失败: ${chrome.runtime.lastError.message}`, 'markdownOutput', 'error');
                } else if (response.error) {
                    log(`Markdown提取错误: ${response.error}`, 'markdownOutput', 'error');
                } else {
                    log(`Markdown提取成功!`, 'markdownOutput', 'success');
                    log(`结果长度: ${response.markdown ? response.markdown.length : 0} 字符`, 'markdownOutput');
                    if (response.chunks) {
                        log(`处理块数: ${response.chunks}`, 'markdownOutput');
                    }
                    if (response.markdown) {
                        log(`Markdown内容预览:\n${response.markdown.substring(0, 500)}...`, 'markdownOutput');
                    }
                }
            });
        }
        
        function testLargeContent() {
            log('测试大内容处理...', 'markdownOutput');
            
            // 生成大量测试内容
            let largeContent = '';
            for (let i = 0; i < 100; i++) {
                largeContent += `段落 ${i + 1}: 这是一个很长的测试段落，用于测试大内容的处理能力。包含了各种字符和标点符号，以及一些特殊的内容结构。\n\n`;
            }
            
            log(`生成大内容，长度: ${largeContent.length} 字符`, 'markdownOutput');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('Chrome扩展API不可用', 'markdownOutput', 'error');
                return;
            }
            
            chrome.runtime.sendMessage({
                action: 'extractMarkdown',
                content: largeContent,
                title: '大内容测试',
                url: window.location.href
            }, (response) => {
                if (chrome.runtime.lastError) {
                    log(`大内容处理失败: ${chrome.runtime.lastError.message}`, 'markdownOutput', 'error');
                } else if (response.error) {
                    log(`大内容处理错误: ${response.error}`, 'markdownOutput', 'error');
                } else {
                    log(`大内容处理成功!`, 'markdownOutput', 'success');
                    log(`结果长度: ${response.markdown ? response.markdown.length : 0} 字符`, 'markdownOutput');
                    if (response.chunks) {
                        log(`处理块数: ${response.chunks}`, 'markdownOutput');
                    }
                }
            });
        }
        
        function diagnoseErrors() {
            log('开始错误诊断...', 'errorOutput');
            
            // 检查常见问题
            const checks = [
                {
                    name: '扩展是否加载',
                    test: () => typeof chrome !== 'undefined' && chrome.runtime,
                    fix: '请确保扩展已正确加载到Chrome中'
                },
                {
                    name: '网络权限',
                    test: () => true, // 这个需要实际测试
                    fix: '检查manifest.json中的host_permissions配置'
                },
                {
                    name: 'API密钥配置',
                    test: () => true, // 这个需要通过扩展检查
                    fix: '确保API密钥正确配置'
                }
            ];
            
            checks.forEach(check => {
                if (check.test()) {
                    log(`✅ ${check.name}: 正常`, 'errorOutput', 'success');
                } else {
                    log(`❌ ${check.name}: 异常`, 'errorOutput', 'error');
                    log(`   修复建议: ${check.fix}`, 'errorOutput', 'warning');
                }
            });
        }
        
        function checkPermissions() {
            log('检查权限...', 'errorOutput');
            
            if (typeof chrome === 'undefined' || !chrome.permissions) {
                log('权限API不可用', 'errorOutput', 'error');
                return;
            }
            
            chrome.permissions.getAll((permissions) => {
                log(`当前权限: ${JSON.stringify(permissions, null, 2)}`, 'errorOutput');
            });
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkExtensionStatus();
            }, 1000);
        });
    </script>
</body>
</html>
