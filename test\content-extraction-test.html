<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="测试作者">
    <meta name="date" content="2024-01-15">
    <meta property="article:published_time" content="2024-01-15T10:00:00Z">
    <title>内容提取测试页面 - 复杂网页结构测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #333;
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
        }
        .nav {
            background: #666;
            padding: 10px;
            margin: 20px -20px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
        }
        .sidebar {
            float: right;
            width: 250px;
            background: #eee;
            padding: 15px;
            margin-left: 20px;
            border-radius: 5px;
        }
        .main-content {
            margin-right: 290px;
        }
        .hidden-content {
            display: none;
        }
        .dynamic-content {
            background: #ffffcc;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #ffcc00;
        }
        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
        }
        .quote {
            border-left: 4px solid #ddd;
            padding-left: 20px;
            margin: 20px 0;
            font-style: italic;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            background: #333;
            color: white;
            padding: 20px;
            margin: 20px -20px -20px -20px;
            text-align: center;
        }
        .ad-banner {
            background: #ff6b6b;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }
        details {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        summary {
            font-weight: bold;
            cursor: pointer;
            padding: 5px;
        }
    </style>
</head>
<body>
    <!-- 页面头部 - 应该被过滤 -->
    <header class="header">
        <h1>网站标题</h1>
        <p>这是网站的头部信息，应该被内容提取器过滤掉</p>
    </header>

    <!-- 导航菜单 - 应该被过滤 -->
    <nav class="nav">
        <a href="#home">首页</a>
        <a href="#about">关于</a>
        <a href="#services">服务</a>
        <a href="#contact">联系</a>
    </nav>

    <div class="container">
        <!-- 侧边栏 - 应该被过滤 -->
        <aside class="sidebar">
            <h3>侧边栏</h3>
            <ul>
                <li><a href="#">相关链接1</a></li>
                <li><a href="#">相关链接2</a></li>
                <li><a href="#">相关链接3</a></li>
            </ul>
            <div class="ad-banner">
                广告内容 - 应该被过滤
            </div>
        </aside>

        <!-- 主要内容区域 - 应该被提取 -->
        <main class="main-content">
            <article>
                <h1>测试文章标题：Chrome插件内容提取优化</h1>
                
                <p>这是一篇用于测试Chrome插件内容提取功能的文章。本文包含了各种复杂的网页元素，用于验证内容提取算法的完整性和准确性。</p>

                <h2>1. 基本文本内容</h2>
                <p>这是一个普通的段落，包含了基本的文本内容。内容提取器应该能够正确识别和提取这些文本。</p>
                
                <p>这是另一个段落，用于测试多段落的提取效果。段落之间应该保持适当的间距和格式。</p>

                <h2>2. 列表内容测试</h2>
                <h3>无序列表</h3>
                <ul>
                    <li>列表项目1：这是第一个列表项</li>
                    <li>列表项目2：这是第二个列表项</li>
                    <li>列表项目3：这是第三个列表项，包含更多的文本内容用于测试</li>
                </ul>

                <h3>有序列表</h3>
                <ol>
                    <li>步骤一：分析现有的内容提取逻辑</li>
                    <li>步骤二：识别可能导致内容遗漏的问题</li>
                    <li>步骤三：优化提取算法</li>
                    <li>步骤四：验证改进效果</li>
                </ol>

                <h2>3. 表格数据测试</h2>
                <table>
                    <thead>
                        <tr>
                            <th>功能</th>
                            <th>优化前</th>
                            <th>优化后</th>
                            <th>改进幅度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>内容识别准确率</td>
                            <td>75%</td>
                            <td>92%</td>
                            <td>+17%</td>
                        </tr>
                        <tr>
                            <td>动态内容提取</td>
                            <td>不支持</td>
                            <td>支持</td>
                            <td>新增功能</td>
                        </tr>
                        <tr>
                            <td>结构化数据提取</td>
                            <td>基础</td>
                            <td>高级</td>
                            <td>显著提升</td>
                        </tr>
                    </tbody>
                </table>

                <h2>4. 代码块测试</h2>
                <p>以下是一个JavaScript代码示例：</p>
                <pre class="code-block"><code>
// 内容提取器类
class ContentExtractor {
  constructor() {
    this.extractionAttempts = 0;
    this.maxAttempts = 3;
  }

  async extractContent() {
    try {
      const content = await this.waitForDynamicContent();
      return this.processContent(content);
    } catch (error) {
      console.error('提取失败:', error);
      throw error;
    }
  }
}
                </code></pre>

                <h2>5. 引用内容测试</h2>
                <blockquote class="quote">
                    "优秀的内容提取算法应该能够准确识别网页的主要内容，同时过滤掉干扰信息，确保用户获得最有价值的信息。"
                    <br><br>
                    —— 内容提取专家
                </blockquote>

                <h2>6. 折叠内容测试</h2>
                <details>
                    <summary>点击展开更多信息</summary>
                    <p>这是折叠在details元素中的内容。优化后的内容提取器应该能够自动展开这些内容并进行提取。</p>
                    <p>折叠内容通常包含重要的补充信息，不应该被忽略。</p>
                </details>

                <h2>7. 动态内容测试</h2>
                <div id="dynamic-container">
                    <p>这里将通过JavaScript动态加载内容...</p>
                </div>

                <h2>8. 隐藏内容测试</h2>
                <div class="hidden-content">
                    <p>这是隐藏的内容，正常情况下不应该被提取。</p>
                </div>

                <div style="display: none;">
                    <p>这也是隐藏的内容，使用内联样式隐藏。</p>
                </div>

                <h2>9. 结论</h2>
                <p>通过这个测试页面，我们可以验证Chrome插件内容提取功能的各个方面，包括：</p>
                <ul>
                    <li>基本文本内容的提取</li>
                    <li>结构化数据（表格、列表）的处理</li>
                    <li>代码块和引用的识别</li>
                    <li>动态内容的等待和提取</li>
                    <li>干扰元素的过滤</li>
                </ul>
                
                <p>这些测试将帮助我们确保内容提取功能的完整性和准确性。</p>
            </article>
        </main>

        <div style="clear: both;"></div>
    </div>

    <!-- 页面底部 - 应该被过滤 -->
    <footer class="footer">
        <p>&copy; 2024 测试网站. 保留所有权利.</p>
        <p>这是页面底部信息，应该被内容提取器过滤掉</p>
    </footer>

    <!-- 模态框 - 应该被过滤 -->
    <div class="modal" id="testModal">
        <div style="background: white; padding: 20px; margin: 100px auto; width: 300px;">
            <h3>模态框内容</h3>
            <p>这是模态框中的内容，应该被过滤掉。</p>
        </div>
    </div>

    <script>
        // 模拟动态内容加载
        setTimeout(() => {
            const container = document.getElementById('dynamic-container');
            if (container) {
                container.innerHTML = `
                    <div class="dynamic-content">
                        <h3>动态加载的内容</h3>
                        <p>这是通过JavaScript动态加载的内容。优化后的内容提取器应该能够等待并提取这些内容。</p>
                        <p>动态内容通常包含重要信息，如用户评论、推荐内容、实时数据等。</p>
                    </div>
                `;
            }
        }, 2000);

        // 添加更多动态内容
        setTimeout(() => {
            const article = document.querySelector('article');
            if (article) {
                const newSection = document.createElement('div');
                newSection.innerHTML = `
                    <h2>10. 延迟加载的内容</h2>
                    <p>这是延迟3秒后加载的内容，用于测试内容提取器的等待机制。</p>
                    <p>在实际网页中，这类内容可能是通过AJAX请求获取的数据。</p>
                `;
                article.appendChild(newSection);
            }
        }, 3000);
    </script>
</body>
</html>
