// Markdown处理器
// 优化Markdown提取和处理逻辑，确保内容完整性

class MarkdownProcessor {
  constructor() {
    this.maxChunkSize = 6000; // 单块最大大小
    this.overlapSize = 200; // 块之间的重叠大小
    this.maxRetries = 3; // 最大重试次数
  }

  // 智能分块处理
  splitContentIntoChunks(content, maxChunkSize = this.maxChunkSize) {
    if (!content || content.length <= maxChunkSize) {
      return [content];
    }

    const chunks = [];
    let currentPosition = 0;

    while (currentPosition < content.length) {
      let chunkEnd = Math.min(currentPosition + maxChunkSize, content.length);
      
      // 如果不是最后一块，尝试在合适的位置分割
      if (chunkEnd < content.length) {
        chunkEnd = this.findBestSplitPoint(content, currentPosition, chunkEnd);
      }

      const chunk = content.substring(currentPosition, chunkEnd);
      chunks.push(chunk);

      // 下一块的起始位置，考虑重叠
      currentPosition = chunkEnd - this.overlapSize;
      if (currentPosition >= content.length) {
        break;
      }
    }

    return chunks;
  }

  // 找到最佳分割点
  findBestSplitPoint(content, start, end) {
    const searchRange = Math.min(300, Math.floor((end - start) * 0.1)); // 搜索范围
    const searchStart = Math.max(start, end - searchRange);

    // 优先级：段落分隔符 > 句号 > 其他标点 > 空格
    const splitPatterns = [
      /\n\s*\n/g,  // 段落分隔符
      /[。！？]\s*/g,  // 中文句号
      /[.!?]\s+/g,  // 英文句号
      /[，；：,;:]\s*/g,  // 其他标点
      /\s+/g  // 空格
    ];

    for (const pattern of splitPatterns) {
      const matches = [...content.substring(searchStart, end).matchAll(pattern)];
      if (matches.length > 0) {
        const lastMatch = matches[matches.length - 1];
        return searchStart + lastMatch.index + lastMatch[0].length;
      }
    }

    // 如果找不到合适的分割点，就在原位置分割
    return end;
  }

  // 处理单个块的Markdown转换
  async processChunk(apiConfig, chunk, title, url, chunkIndex, totalChunks) {
    const chunkInfo = totalChunks > 1 ? `（第${chunkIndex}/${totalChunks}部分）` : '';
    
    const prompt = `请将以下网页内容转换为标准的Markdown格式。要求：

1. 保持原文的结构层次，使用合适的标题级别（# ## ### 等）
2. 保留重要的文本格式（粗体、斜体、链接等）
3. 将列表转换为Markdown列表格式
4. 保留代码块和引用块的格式
5. 移除广告、导航菜单等无关内容
6. 确保输出的Markdown格式规范且易读
7. 如果有图片，保留图片的alt文本信息
8. 保持内容的完整性和连贯性

网页标题：${title}${chunkInfo}
网页链接：${url}

网页内容：
${chunk}

请直接输出转换后的Markdown内容，不要添加额外的说明文字：`;

    return await this.callAPIWithRetry(apiConfig, prompt);
  }

  // 带重试的API调用
  async callAPIWithRetry(apiConfig, prompt, retryCount = 0) {
    try {
      const requestBody = {
        model: apiConfig.model || 'qwen-plus',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的内容转换助手，能够准确将网页内容转换为标准的Markdown格式。请用中文回答。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: apiConfig.temperature || 0.7,
        max_tokens: apiConfig.maxTokens || 2000,
        top_p: 0.8
      };

      console.log(`MarkdownProcessor API调用 (尝试 ${retryCount + 1}):`, {
        url: `${apiConfig.baseUrl}/chat/completions`,
        model: requestBody.model,
        promptLength: prompt.length,
        apiKeyPrefix: apiConfig.apiKey ? apiConfig.apiKey.substring(0, 10) + '...' : 'none'
      });

      const response = await fetch(`${apiConfig.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiConfig.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      console.log(`API响应状态:`, response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API错误响应:', errorText);
        throw new Error(`API调用失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      console.log('API响应数据结构:', {
        hasChoices: !!data.choices,
        choicesLength: data.choices ? data.choices.length : 0,
        hasMessage: data.choices && data.choices[0] && !!data.choices[0].message,
        contentLength: data.choices && data.choices[0] && data.choices[0].message ? data.choices[0].message.content.length : 0
      });

      if (data.choices && data.choices[0] && data.choices[0].message) {
        console.log('API调用成功，返回内容长度:', data.choices[0].message.content.length);
        return data.choices[0].message.content;
      } else {
        console.error('API响应格式错误，完整响应:', data);
        throw new Error('API响应格式错误');
      }

    } catch (error) {
      console.error(`API调用失败 (尝试 ${retryCount + 1}):`, error);
      console.error('错误详情:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      
      if (retryCount < this.maxRetries) {
        const delay = 1000 * Math.pow(2, retryCount); // 指数退避
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.callAPIWithRetry(apiConfig, prompt, retryCount + 1);
      }
      
      throw error;
    }
  }

  // 智能合并Markdown块
  combineMarkdownChunks(markdownChunks, title, url) {
    if (!markdownChunks || markdownChunks.length === 0) {
      return '';
    }

    if (markdownChunks.length === 1) {
      return this.addMarkdownHeader(markdownChunks[0], title, url);
    }

    // 合并多个块
    const processedChunks = markdownChunks.map((chunk, index) => {
      return this.cleanupChunk(chunk, index, markdownChunks.length);
    });

    // 移除重复内容
    const deduplicatedChunks = this.removeDuplicateContent(processedChunks);

    // 合并内容
    const combinedContent = deduplicatedChunks.join('\n\n---\n\n');

    return this.addMarkdownHeader(combinedContent, title, url);
  }

  // 清理单个块
  cleanupChunk(chunk, index, totalChunks) {
    if (!chunk) return '';

    let cleaned = chunk.trim();

    // 移除可能的重复标题
    if (index > 0) {
      // 移除开头的重复标题
      cleaned = cleaned.replace(/^#+\s+.*?\n/, '');
    }

    // 移除末尾的不完整句子（只对非最后一块）
    if (index < totalChunks - 1) {
      const lines = cleaned.split('\n');
      const lastLine = lines[lines.length - 1];
      
      // 如果最后一行不以标点符号结尾，可能是不完整的
      if (lastLine && !/[。！？.!?]$/.test(lastLine.trim())) {
        lines.pop();
        cleaned = lines.join('\n');
      }
    }

    return cleaned;
  }

  // 移除重复内容
  removeDuplicateContent(chunks) {
    if (chunks.length <= 1) return chunks;

    const result = [chunks[0]];

    for (let i = 1; i < chunks.length; i++) {
      const currentChunk = chunks[i];
      const previousChunk = result[result.length - 1];

      // 检查是否有重复的开头
      const overlap = this.findOverlap(previousChunk, currentChunk);
      
      if (overlap > 50) { // 如果重叠超过50个字符
        // 移除重复部分
        const deduplicated = this.removeOverlap(previousChunk, currentChunk, overlap);
        result.push(deduplicated);
      } else {
        result.push(currentChunk);
      }
    }

    return result;
  }

  // 查找重叠内容
  findOverlap(chunk1, chunk2) {
    const end1 = chunk1.slice(-200); // 取chunk1的末尾200字符
    const start2 = chunk2.slice(0, 200); // 取chunk2的开头200字符

    let maxOverlap = 0;
    
    for (let i = 10; i <= Math.min(end1.length, start2.length); i++) {
      const suffix = end1.slice(-i);
      const prefix = start2.slice(0, i);
      
      if (suffix === prefix) {
        maxOverlap = i;
      }
    }

    return maxOverlap;
  }

  // 移除重叠部分
  removeOverlap(chunk1, chunk2, overlapLength) {
    return chunk2.slice(overlapLength);
  }

  // 添加Markdown头部
  addMarkdownHeader(content, title, url) {
    const header = `# ${title}

> 来源：${url}
> 生成时间：${new Date().toLocaleString('zh-CN')}

---

`;

    return header + content;
  }

  // 验证Markdown内容完整性
  validateMarkdownContent(originalContent, markdownContent) {
    const originalLength = originalContent.length;
    const markdownLength = markdownContent.length;
    
    // 简单的完整性检查
    const ratio = markdownLength / originalLength;
    
    return {
      isComplete: ratio > 0.3 && ratio < 3, // 合理的长度比例
      originalLength,
      markdownLength,
      ratio,
      warnings: ratio < 0.3 ? ['内容可能不完整'] : ratio > 3 ? ['内容可能包含重复'] : []
    };
  }
}

// 导出单例
const markdownProcessor = new MarkdownProcessor();
