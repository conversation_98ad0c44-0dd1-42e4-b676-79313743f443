// 智能网页总结助手 - 内容脚本
// 负责从网页中提取主要文本内容

class ContentExtractor {
  constructor() {
    this.extractionAttempts = 0;
    this.maxAttempts = 3;
    this.dynamicContentTimeout = 5000; // 5秒等待动态内容
    this.observerTimeout = 3000; // 3秒观察DOM变化
    this.initializeExtractor();
  }

  // 初始化内容提取器
  initializeExtractor() {
    // 监听来自background script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'extractContent') {
        this.handleContentExtraction(sendResponse);
      }
      return true;
    });

    // 页面加载完成后自动提取内容（可选）
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.cachePageContent();
      });
    } else {
      this.cachePageContent();
    }
  }

  // 处理内容提取请求
  async handleContentExtraction(sendResponse) {
    try {
      // 等待动态内容加载
      await this.waitForDynamicContent();

      // 提取内容
      const content = await this.extractPageContentWithRetry();
      sendResponse({ success: true, content: content });
    } catch (error) {
      console.error('内容提取失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  // 等待动态内容加载
  async waitForDynamicContent() {
    return new Promise((resolve) => {
      let timeoutId;
      let observer;
      let resolved = false;

      const resolveOnce = () => {
        if (resolved) return;
        resolved = true;

        if (observer) observer.disconnect();
        if (timeoutId) clearTimeout(timeoutId);
        resolve();
      };

      // 设置超时
      timeoutId = setTimeout(resolveOnce, this.dynamicContentTimeout);

      // 观察DOM变化
      if (window.MutationObserver) {
        observer = new MutationObserver((mutations) => {
          // 检查是否有重要的内容变化
          const hasSignificantChanges = mutations.some(mutation => {
            return mutation.type === 'childList' &&
                   mutation.addedNodes.length > 0 &&
                   Array.from(mutation.addedNodes).some(node =>
                     node.nodeType === Node.ELEMENT_NODE &&
                     this.isContentNode(node)
                   );
          });

          if (hasSignificantChanges) {
            // 等待一小段时间让内容稳定
            setTimeout(resolveOnce, this.observerTimeout);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }

      // 如果页面已经稳定，立即解析
      if (document.readyState === 'complete') {
        setTimeout(resolveOnce, 500);
      }
    });
  }

  // 检查节点是否为内容节点
  isContentNode(node) {
    if (!node.tagName) return false;

    const contentTags = ['P', 'DIV', 'ARTICLE', 'SECTION', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'UL', 'OL', 'LI'];
    const hasContentTag = contentTags.includes(node.tagName);
    const hasText = node.textContent && node.textContent.trim().length > 20;

    return hasContentTag && hasText;
  }

  // 带重试的内容提取
  async extractPageContentWithRetry() {
    for (let attempt = 1; attempt <= this.maxAttempts; attempt++) {
      try {
        const content = this.extractPageContent();

        // 验证内容质量
        if (this.validateContentQuality(content)) {
          return content;
        }

        if (attempt < this.maxAttempts) {
          console.log(`内容质量不佳，进行第${attempt + 1}次尝试...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        if (attempt === this.maxAttempts) {
          throw error;
        }
        console.warn(`第${attempt}次提取失败，重试中...`, error);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error('多次尝试后仍无法提取有效内容');
  }

  // 验证内容质量
  validateContentQuality(content) {
    if (!content || !content.content) return false;

    const wordCount = this.countWords(content.content);
    const hasStructure = content.structure && content.structure.hasStructure;

    // 至少要有50个词或者有明确的结构
    return wordCount >= 50 || hasStructure;
  }

  // 提取页面主要内容
  extractPageContent() {
    console.log('开始提取页面内容...');

    // 创建页面内容的副本以避免修改原页面
    const pageClone = document.cloneNode(true);

    // 预处理：展开可能的折叠内容
    this.expandCollapsedContent(pageClone);

    // 移除不需要的元素
    this.removeUnwantedElements(pageClone);

    // 查找主要内容区域（使用多种策略）
    const mainContent = this.findMainContentAdvanced(pageClone);

    // 提取结构化内容
    const structuredContent = this.extractStructuredContent(mainContent);

    // 提取和清理文本
    const textContent = this.extractAndCleanText(mainContent);

    // 分析内容结构
    const contentAnalysis = this.analyzeContentAdvanced(mainContent);

    // 提取额外信息
    const metadata = this.extractMetadata();

    const result = {
      content: textContent,
      structuredContent: structuredContent,
      title: document.title,
      url: window.location.href,
      wordCount: this.countWords(textContent),
      language: this.detectLanguage(textContent),
      structure: contentAnalysis,
      metadata: metadata,
      extractedAt: new Date().toISOString(),
      extractionMethod: 'advanced'
    };

    // 进行完整性验证
    const originalContent = document.body.textContent || '';
    const validation = this.validateContentCompleteness(originalContent, result);
    result.validation = validation;

    // 如果验证发现问题，记录警告
    if (!validation.isComplete || validation.warnings.length > 0) {
      console.warn('内容提取完整性检查发现问题:', validation);
      result.completenessReport = this.generateCompletenessReport(validation);
    }

    console.log('内容提取完成:', {
      wordCount: result.wordCount,
      retentionRatio: validation.metrics.retentionRatio,
      warnings: validation.warnings.length,
      isComplete: validation.isComplete
    });

    return result;
  }

  // 展开折叠内容
  expandCollapsedContent(doc) {
    // 展开details元素
    const details = doc.querySelectorAll('details');
    details.forEach(detail => {
      detail.open = true;
    });

    // 点击展开按钮（模拟）
    const expandButtons = doc.querySelectorAll('[aria-expanded="false"], .expand, .show-more, .read-more');
    expandButtons.forEach(button => {
      try {
        // 尝试触发展开
        if (button.click && typeof button.click === 'function') {
          button.click();
        }
        // 或者直接设置属性
        if (button.hasAttribute('aria-expanded')) {
          button.setAttribute('aria-expanded', 'true');
        }
      } catch (error) {
        // 忽略点击错误
      }
    });

    // 移除display:none样式（谨慎操作）
    const hiddenElements = doc.querySelectorAll('[style*="display: none"], [style*="display:none"]');
    hiddenElements.forEach(element => {
      // 只对可能包含内容的元素操作
      if (this.mightContainContent(element)) {
        element.style.display = '';
      }
    });
  }

  // 判断元素是否可能包含内容
  mightContainContent(element) {
    const contentIndicators = ['content', 'text', 'article', 'post', 'story', 'body'];
    const className = (element.className || '').toLowerCase();
    const id = (element.id || '').toLowerCase();

    return contentIndicators.some(indicator =>
      className.includes(indicator) || id.includes(indicator)
    );
  }

  // 移除不需要的元素
  removeUnwantedElements(doc) {
    const unwantedSelectors = [
      // 脚本和样式
      'script', 'style', 'noscript',
      
      // 导航和菜单
      'nav', 'header', 'footer', 'aside',
      '.navigation', '.nav', '.menu', '.sidebar',
      '.breadcrumb', '.breadcrumbs',
      
      // 广告和推广
      '.advertisement', '.ads', '.ad', '.advert',
      '.sponsored', '.promotion', '.banner',
      '[class*="ad-"]', '[id*="ad-"]',
      '[class*="ads-"]', '[id*="ads-"]',
      
      // 社交和分享
      '.social', '.social-share', '.share-buttons',
      '.social-media', '.follow-us',
      
      // 评论和互动
      '.comments', '.comment', '.discussion',
      '.reviews', '.rating', '.feedback',
      
      // 弹窗和模态框
      '.popup', '.modal', '.overlay', '.lightbox',
      '.newsletter', '.subscription',
      
      // 相关内容和推荐
      '.related', '.recommended', '.suggestions',
      '.more-stories', '.you-might-like',
      
      // 工具栏和控件
      '.toolbar', '.controls', '.player-controls',
      '.video-controls', '.audio-controls',
      
      // 其他干扰元素
      '.cookie-notice', '.gdpr-notice',
      '.loading', '.spinner', '.placeholder',
      'iframe', 'embed', 'object'
    ];

    unwantedSelectors.forEach(selector => {
      try {
        const elements = doc.querySelectorAll(selector);
        elements.forEach(element => {
          if (element && element.parentNode) {
            element.parentNode.removeChild(element);
          }
        });
      } catch (error) {
        console.warn(`移除元素失败: ${selector}`, error);
      }
    });

    // 移除隐藏元素
    this.removeHiddenElements(doc);
    
    // 移除空元素
    this.removeEmptyElements(doc);
  }

  // 移除隐藏元素
  removeHiddenElements(doc) {
    const allElements = doc.querySelectorAll('*');
    allElements.forEach(element => {
      try {
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || 
            style.visibility === 'hidden' || 
            style.opacity === '0' ||
            element.hidden) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }
      } catch (error) {
        // 忽略样式获取错误
      }
    });
  }

  // 移除空元素
  removeEmptyElements(doc) {
    const emptyElements = doc.querySelectorAll('div, span, p, section, article');
    emptyElements.forEach(element => {
      if (element.textContent.trim() === '' && 
          element.children.length === 0) {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      }
    });
  }

  // 高级主要内容查找
  findMainContentAdvanced(doc) {
    // 第一阶段：尝试语义化和常见选择器
    const primarySelectors = [
      'main',
      'article',
      '[role="main"]',
      '[role="article"]',
      '.content',
      '.main-content',
      '.post-content',
      '.article-content',
      '.entry-content',
      '.page-content'
    ];

    for (const selector of primarySelectors) {
      const element = doc.querySelector(selector);
      if (element && this.hasSignificantContent(element)) {
        console.log(`找到主要内容区域: ${selector}`);
        return element;
      }
    }

    // 第二阶段：尝试更多特定网站的选择器
    const secondarySelectors = [
      '.story-body', '.article-body', '.post-body', '.news-content',
      '.blog-post', '.post', '.entry', '.text-content',
      '.content-body', '.main-text', '.article-text',
      '.story-content', '.news-body', '.blog-content',
      '#content', '#main', '#main-content', '#article', '#post',
      '.container .content', '.wrapper .content',
      '[data-content]', '[data-article]', '[data-post]'
    ];

    for (const selector of secondarySelectors) {
      const element = doc.querySelector(selector);
      if (element && this.hasSignificantContent(element)) {
        console.log(`找到主要内容区域: ${selector}`);
        return element;
      }
    }

    // 第三阶段：智能分析最佳内容区域
    const bestContent = this.findBestContentByAnalysis(doc);
    if (bestContent) {
      console.log('通过智能分析找到主要内容区域');
      return bestContent;
    }

    // 最后阶段：使用body但进一步过滤
    console.log('使用body作为内容区域');
    return this.filterBodyContent(doc.body || doc.documentElement);
  }

  // 通过分析找到最佳内容区域
  findBestContentByAnalysis(doc) {
    const candidates = [];

    // 收集候选元素
    const potentialContainers = doc.querySelectorAll('div, section, article, main');

    potentialContainers.forEach(element => {
      const score = this.calculateContentScore(element);
      if (score > 0) {
        candidates.push({ element, score });
      }
    });

    // 按分数排序
    candidates.sort((a, b) => b.score - a.score);

    // 返回得分最高的元素
    return candidates.length > 0 ? candidates[0].element : null;
  }

  // 计算内容分数
  calculateContentScore(element) {
    let score = 0;

    // 文本长度分数
    const textLength = (element.textContent || '').length;
    score += Math.min(textLength / 100, 50); // 最多50分

    // 段落数量分数
    const paragraphs = element.querySelectorAll('p');
    score += Math.min(paragraphs.length * 2, 20); // 最多20分

    // 标题数量分数
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    score += Math.min(headings.length * 3, 15); // 最多15分

    // 列表分数
    const lists = element.querySelectorAll('ul, ol');
    score += Math.min(lists.length * 2, 10); // 最多10分

    // 类名和ID分数
    const className = (element.className || '').toLowerCase();
    const id = (element.id || '').toLowerCase();
    const contentKeywords = ['content', 'article', 'post', 'story', 'main', 'body', 'text'];

    contentKeywords.forEach(keyword => {
      if (className.includes(keyword) || id.includes(keyword)) {
        score += 5;
      }
    });

    // 负分项：导航、侧边栏等
    const negativeKeywords = ['nav', 'sidebar', 'menu', 'header', 'footer', 'ad'];
    negativeKeywords.forEach(keyword => {
      if (className.includes(keyword) || id.includes(keyword)) {
        score -= 10;
      }
    });

    return score;
  }

  // 检查元素是否包含有意义的内容
  hasSignificantContent(element) {
    const text = element.textContent || '';
    const wordCount = this.countWords(text);
    return wordCount > 50; // 至少50个词
  }

  // 过滤body内容
  filterBodyContent(body) {
    // 创建一个新的容器
    const filteredContent = document.createElement('div');
    
    // 查找所有可能包含主要内容的元素
    const contentElements = body.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, blockquote, pre');
    
    contentElements.forEach(element => {
      if (this.isContentElement(element)) {
        filteredContent.appendChild(element.cloneNode(true));
      }
    });
    
    return filteredContent;
  }

  // 判断是否为内容元素
  isContentElement(element) {
    const text = element.textContent || '';
    const wordCount = this.countWords(text);
    
    // 过滤太短的文本
    if (wordCount < 3) return false;
    
    // 过滤导航链接
    if (element.tagName === 'A' && wordCount < 10) return false;
    
    // 过滤可能的菜单项
    const className = element.className || '';
    const id = element.id || '';
    const unwantedPatterns = ['menu', 'nav', 'header', 'footer', 'sidebar', 'ad'];
    
    for (const pattern of unwantedPatterns) {
      if (className.toLowerCase().includes(pattern) || 
          id.toLowerCase().includes(pattern)) {
        return false;
      }
    }
    
    return true;
  }

  // 提取和清理文本
  extractAndCleanText(element) {
    let text = element.textContent || element.innerText || '';
    
    // 清理文本
    text = text
      // 移除多余的空白字符
      .replace(/\s+/g, ' ')
      // 移除多余的换行
      .replace(/\n\s*\n/g, '\n')
      // 移除行首行尾空白
      .replace(/^\s+|\s+$/gm, '')
      // 移除特殊字符（保留基本标点）
      .replace(/[^\w\s\u4e00-\u9fff.,!?;:()[\]{}""''—–-]/g, '')
      .trim();
    
    // 限制内容长度（避免超过API限制）
    const maxLength = 10000;
    if (text.length > maxLength) {
      text = text.substring(0, maxLength) + '...';
      console.log(`内容过长，已截断至${maxLength}字符`);
    }
    
    return text;
  }

  // 提取结构化内容
  extractStructuredContent(element) {
    const structured = {
      headings: [],
      paragraphs: [],
      lists: [],
      tables: [],
      codeBlocks: [],
      quotes: [],
      images: []
    };

    // 提取标题
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      structured.headings.push({
        level: parseInt(heading.tagName.charAt(1)),
        text: heading.textContent.trim(),
        id: heading.id || null
      });
    });

    // 提取段落
    const paragraphs = element.querySelectorAll('p');
    paragraphs.forEach(p => {
      const text = p.textContent.trim();
      if (text.length > 10) { // 过滤太短的段落
        structured.paragraphs.push(text);
      }
    });

    // 提取列表
    const lists = element.querySelectorAll('ul, ol');
    lists.forEach(list => {
      const items = Array.from(list.querySelectorAll('li')).map(li => li.textContent.trim());
      if (items.length > 0) {
        structured.lists.push({
          type: list.tagName.toLowerCase(),
          items: items
        });
      }
    });

    // 提取表格
    const tables = element.querySelectorAll('table');
    tables.forEach(table => {
      const rows = Array.from(table.querySelectorAll('tr')).map(tr => {
        return Array.from(tr.querySelectorAll('td, th')).map(cell => cell.textContent.trim());
      });
      if (rows.length > 0) {
        structured.tables.push(rows);
      }
    });

    // 提取代码块
    const codeBlocks = element.querySelectorAll('pre, code');
    codeBlocks.forEach(code => {
      const text = code.textContent.trim();
      if (text.length > 5) {
        structured.codeBlocks.push(text);
      }
    });

    // 提取引用
    const quotes = element.querySelectorAll('blockquote');
    quotes.forEach(quote => {
      const text = quote.textContent.trim();
      if (text.length > 10) {
        structured.quotes.push(text);
      }
    });

    // 提取图片信息
    const images = element.querySelectorAll('img');
    images.forEach(img => {
      structured.images.push({
        src: img.src || '',
        alt: img.alt || '',
        title: img.title || ''
      });
    });

    return structured;
  }

  // 高级内容分析
  analyzeContentAdvanced(element) {
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const paragraphs = element.querySelectorAll('p');
    const lists = element.querySelectorAll('ul, ol');
    const images = element.querySelectorAll('img');
    const links = element.querySelectorAll('a');
    const tables = element.querySelectorAll('table');
    const codeBlocks = element.querySelectorAll('pre, code');

    // 计算内容密度
    const totalText = element.textContent || '';
    const totalElements = element.querySelectorAll('*').length;
    const contentDensity = totalElements > 0 ? totalText.length / totalElements : 0;

    // 分析标题层次
    const headingLevels = Array.from(headings).map(h => parseInt(h.tagName.charAt(1)));
    const hasHierarchy = headingLevels.length > 1 && Math.max(...headingLevels) - Math.min(...headingLevels) > 0;

    return {
      headingCount: headings.length,
      paragraphCount: paragraphs.length,
      listCount: lists.length,
      imageCount: images.length,
      linkCount: links.length,
      tableCount: tables.length,
      codeBlockCount: codeBlocks.length,
      hasStructure: headings.length > 0,
      hasHierarchy: hasHierarchy,
      contentDensity: contentDensity,
      estimatedReadingTime: Math.ceil(this.countWords(totalText) / 200), // 假设每分钟200词
      contentType: this.detectContentType(element)
    };
  }

  // 检测内容类型
  detectContentType(element) {
    const text = element.textContent || '';
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const codeBlocks = element.querySelectorAll('pre, code');
    const tables = element.querySelectorAll('table');

    if (codeBlocks.length > 3) return 'technical';
    if (tables.length > 2) return 'data';
    if (headings.length > 5) return 'article';
    if (text.length > 5000) return 'long-form';
    if (text.length < 500) return 'short-form';

    return 'general';
  }

  // 提取元数据
  extractMetadata() {
    const metadata = {};

    // 提取meta标签
    const metaTags = document.querySelectorAll('meta');
    metaTags.forEach(meta => {
      const name = meta.getAttribute('name') || meta.getAttribute('property');
      const content = meta.getAttribute('content');
      if (name && content) {
        metadata[name] = content;
      }
    });

    // 提取结构化数据
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    const structuredData = [];
    jsonLdScripts.forEach(script => {
      try {
        const data = JSON.parse(script.textContent);
        structuredData.push(data);
      } catch (error) {
        // 忽略解析错误
      }
    });

    if (structuredData.length > 0) {
      metadata.structuredData = structuredData;
    }

    // 提取作者信息
    const authorMeta = document.querySelector('meta[name="author"]');
    if (authorMeta) {
      metadata.author = authorMeta.getAttribute('content');
    }

    // 提取发布日期
    const dateMeta = document.querySelector('meta[name="date"], meta[property="article:published_time"]');
    if (dateMeta) {
      metadata.publishDate = dateMeta.getAttribute('content');
    }

    return metadata;
  }

  // 内容完整性验证
  validateContentCompleteness(originalContent, extractedContent) {
    const validation = {
      isComplete: true,
      warnings: [],
      metrics: {},
      suggestions: []
    };

    // 计算基本指标
    const originalWordCount = this.countWords(originalContent);
    const extractedWordCount = this.countWords(extractedContent.content);
    const retentionRatio = originalWordCount > 0 ? extractedWordCount / originalWordCount : 0;

    validation.metrics = {
      originalWordCount,
      extractedWordCount,
      retentionRatio: Math.round(retentionRatio * 100) / 100,
      compressionRatio: Math.round((1 - retentionRatio) * 100) / 100
    };

    // 检查内容保留率
    if (retentionRatio < 0.3) {
      validation.isComplete = false;
      validation.warnings.push('内容保留率过低，可能遗漏了重要信息');
      validation.suggestions.push('建议检查页面是否包含动态加载内容');
    } else if (retentionRatio < 0.5) {
      validation.warnings.push('内容保留率较低，请确认提取结果的完整性');
    }

    // 检查结构完整性
    if (!extractedContent.structure || !extractedContent.structure.hasStructure) {
      validation.warnings.push('未检测到明显的内容结构，可能是页面结构复杂');
      validation.suggestions.push('建议手动检查页面的主要内容区域');
    }

    // 检查是否有重要元素被遗漏
    const originalDoc = document;
    const importantElements = this.checkImportantElements(originalDoc);

    if (importantElements.hasTables && extractedContent.structure.tableCount === 0) {
      validation.warnings.push('检测到页面包含表格，但提取结果中未包含');
    }

    if (importantElements.hasCodeBlocks && extractedContent.structure.codeBlockCount === 0) {
      validation.warnings.push('检测到页面包含代码块，但提取结果中未包含');
    }

    if (importantElements.hasLists && extractedContent.structure.listCount === 0) {
      validation.warnings.push('检测到页面包含列表，但提取结果中未包含');
    }

    // 检查内容类型匹配
    const expectedContentType = this.detectExpectedContentType(originalDoc);
    if (expectedContentType !== extractedContent.structure.contentType) {
      validation.warnings.push(`内容类型不匹配：期望 ${expectedContentType}，实际 ${extractedContent.structure.contentType}`);
    }

    return validation;
  }

  // 检查重要元素
  checkImportantElements(doc) {
    return {
      hasTables: doc.querySelectorAll('table').length > 0,
      hasCodeBlocks: doc.querySelectorAll('pre, code').length > 0,
      hasLists: doc.querySelectorAll('ul, ol').length > 0,
      hasImages: doc.querySelectorAll('img').length > 0,
      hasVideos: doc.querySelectorAll('video, iframe[src*="youtube"], iframe[src*="vimeo"]').length > 0
    };
  }

  // 检测期望的内容类型
  detectExpectedContentType(doc) {
    const url = window.location.href.toLowerCase();
    const title = document.title.toLowerCase();

    // 基于URL和标题的启发式检测
    if (url.includes('github.com') || url.includes('stackoverflow.com') ||
        title.includes('code') || title.includes('api')) {
      return 'technical';
    }

    if (url.includes('news') || url.includes('article') ||
        title.includes('news') || title.includes('报道')) {
      return 'article';
    }

    if (doc.querySelectorAll('table').length > 2) {
      return 'data';
    }

    const textLength = doc.body.textContent.length;
    if (textLength > 5000) {
      return 'long-form';
    } else if (textLength < 500) {
      return 'short-form';
    }

    return 'general';
  }

  // 生成完整性报告
  generateCompletenessReport(validation) {
    let report = `## 内容提取完整性报告\n\n`;

    report += `### 基本指标\n`;
    report += `- 原始内容词数: ${validation.metrics.originalWordCount}\n`;
    report += `- 提取内容词数: ${validation.metrics.extractedWordCount}\n`;
    report += `- 内容保留率: ${(validation.metrics.retentionRatio * 100).toFixed(1)}%\n`;
    report += `- 压缩率: ${(validation.metrics.compressionRatio * 100).toFixed(1)}%\n\n`;

    if (validation.warnings.length > 0) {
      report += `### ⚠️ 警告信息\n`;
      validation.warnings.forEach(warning => {
        report += `- ${warning}\n`;
      });
      report += `\n`;
    }

    if (validation.suggestions.length > 0) {
      report += `### 💡 建议\n`;
      validation.suggestions.forEach(suggestion => {
        report += `- ${suggestion}\n`;
      });
      report += `\n`;
    }

    report += `### 总体评估\n`;
    if (validation.isComplete) {
      report += `✅ 内容提取基本完整\n`;
    } else {
      report += `❌ 内容提取可能不完整，建议进一步检查\n`;
    }

    return report;
  }

  // 统计词数
  countWords(text) {
    if (!text) return 0;
    
    // 中文字符按字符计算，英文按单词计算
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
    const englishWordCount = englishWords ? englishWords.length : 0;
    
    return chineseChars + englishWordCount;
  }

  // 检测语言
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalChars = text.length;
    
    if (chineseChars / totalChars > 0.3) {
      return 'zh';
    } else {
      return 'en';
    }
  }

  // 缓存页面内容（可选功能）
  cachePageContent() {
    try {
      const content = this.extractPageContent();
      // 可以将内容缓存到localStorage或发送给background script
      console.log('页面内容已缓存');
    } catch (error) {
      console.error('缓存页面内容失败:', error);
    }
  }
}

// 初始化内容提取器
if (typeof window !== 'undefined') {
  new ContentExtractor();
}
