// Service Worker for Chrome Extension
// 处理扩展的后台逻辑

// 导入钉钉认证相关模块
importScripts('../utils/dingtalk-security.js');
importScripts('../utils/dingtalk-auth.js');
importScripts('../utils/dingtalk-error-handler.js');
importScripts('../utils/storage-quota-manager.js');
importScripts('../utils/markdown-processor.js');

// 硬编码的API配置（更新为有效的配置）
const HARDCODED_API_CONFIG = {
  apiKey: 'sk-466900693bb54313bb9c9a5feb986eb4', // 需要验证此密钥是否有效
  baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
};

// API配置验证
async function validateAPIConfig(apiConfig) {
  try {
    console.log('验证API配置:', {
      provider: apiConfig.provider,
      baseUrl: apiConfig.baseUrl,
      hasApiKey: !!apiConfig.apiKey,
      apiKeyPrefix: apiConfig.apiKey ? apiConfig.apiKey.substring(0, 10) + '...' : 'none',
      model: apiConfig.model
    });

    const testUrl = `${apiConfig.baseUrl}/models`;
    console.log('发送验证请求到:', testUrl);

    const testResponse = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiConfig.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('验证响应状态:', testResponse.status, testResponse.statusText);

    if (testResponse.ok) {
      console.log('API配置验证成功');
      return true;
    } else {
      const errorText = await testResponse.text();
      console.error('API配置验证失败:', {
        status: testResponse.status,
        statusText: testResponse.statusText,
        errorText: errorText
      });
      return false;
    }
  } catch (error) {
    console.error('API配置验证错误:', error);
    console.error('错误详情:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    return false;
  }
}

// 全局钉钉认证管理器实例
let dingTalkAuthManager = null;
let dingTalkErrorHandler = null;

// 立即初始化钉钉认证管理器（确保Cookie监听器尽早设置）
async function initializeDingTalkAuth() {
  try {
    if (!dingTalkAuthManager) {
      dingTalkAuthManager = new DingTalkAuthManager();
      dingTalkErrorHandler = new DingTalkErrorHandler();
      console.log('钉钉认证管理器初始化完成');
    }
  } catch (error) {
    console.error('钉钉认证管理器初始化失败:', error);
  }
}

// Service Worker启动时立即初始化
initializeDingTalkAuth();

// Service Worker启动事件（确保每次激活都重新设置监听器）
chrome.runtime.onStartup.addListener(async () => {
  console.log('Service Worker启动，重新初始化钉钉认证管理器');
  await initializeDingTalkAuth();
});

// 安装事件
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('智能网页总结助手已安装');

  // 确保钉钉认证管理器已初始化
  await initializeDingTalkAuth();

  // 设置默认配置
  chrome.storage.sync.set({
    apiConfig: {
      provider: 'qwen',
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl,
      model: 'qwen-plus'
    },
    promptTemplates: {
      default: {
        name: '默认总结',
        prompt: '请对以下内容进行总结，提取关键信息和要点：\n\n{content}'
      },
      news: {
        name: '新闻总结',
        prompt: '请总结这篇新闻的主要内容，包括：\n1. 核心事件\n2. 关键人物\n3. 时间地点\n4. 影响和意义\n\n内容：{content}'
      },
      academic: {
        name: '学术文章',
        prompt: '请总结这篇学术文章，包括：\n1. 研究目的和问题\n2. 主要方法\n3. 核心发现\n4. 结论和意义\n\n内容：{content}'
      },
      technical: {
        name: '技术文档',
        prompt: '请总结这份技术文档的要点：\n1. 主要功能特性\n2. 使用方法\n3. 注意事项\n4. 适用场景\n\n内容：{content}'
      }
    },
    selectedTemplate: 'default',
    uiSettings: {
      theme: 'dark',
      sidebarWidth: 380,
      autoSummarize: false
    },
    // 新增钉钉认证相关默认配置
    dingTalkConfig: {
      enabled: true,
      autoLogin: false,
      environment: 'production'
    }
  });
});

// 处理扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  try {
    // 打开侧边栏
    await chrome.sidePanel.open({ tabId: tab.id });
  } catch (error) {
    console.error('打开侧边栏失败:', error);
  }
});

// 处理来自content script和sidebar的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到消息:', request);

  // 处理钉钉认证状态变化广播消息
  if (request.type === 'DINGTALK_AUTH_STATUS_CHANGED') {
    // 这是来自认证管理器的广播消息，转发给所有扩展页面
    console.log('转发钉钉认证状态变化消息:', request.data);
    return false; // 不需要响应
  }

  switch (request.action) {
    case 'extractContent':
      handleContentExtraction(request, sender, sendResponse);
      break;
    case 'summarizeContent':
      handleSummarization(request, sender, sendResponse);
      break;
    case 'extractMarkdown':
      handleMarkdownExtraction(request, sender, sendResponse);
      break;
    case 'getConfig':
      handleGetConfig(sendResponse);
      break;
    case 'saveConfig':
      handleSaveConfig(request.config, sendResponse);
      break;
    case 'testConnection':
      handleTestConnection(request.apiConfig, sendResponse);
      break;
    case 'exportConfig':
      handleExportConfig(sendResponse);
      break;
    case 'importConfig':
      handleImportConfig(request.importData, sendResponse);
      break;
    case 'clearHistory':
      handleClearHistory(sendResponse);
      break;
    case 'resetSettings':
      handleResetSettings(sendResponse);
      break;
    case 'addHistoryRecord':
      handleAddHistoryRecord(request.record, sendResponse);
      break;
    // 钉钉认证相关消息处理
    case 'getDingTalkAuthStatus':
      handleGetDingTalkAuthStatus(sendResponse);
      break;
    case 'initiateDingTalkLogin':
      handleInitiateDingTalkLogin(sendResponse);
      break;
    case 'selectDingTalkOrg':
      handleSelectDingTalkOrg(request.corpId, sendResponse);
      break;
    case 'dingTalkLogout':
      handleDingTalkLogout(sendResponse);
      break;
    case 'refreshDingTalkAuth':
      handleRefreshDingTalkAuth(sendResponse);
      break;
    // 调试相关消息处理
    case 'validateDingTalkCookies':
      handleValidateDingTalkCookies(sendResponse);
      break;
    case 'forceDingTalkAuthCheck':
      handleForceDingTalkAuthCheck(sendResponse);
      break;
    case 'getDingTalkDebugInfo':
      handleGetDingTalkDebugInfo(sendResponse);
      break;
    case 'testAPIConfig':
      handleTestAPIConfig(sendResponse);
      break;
    case 'ping':
      sendResponse({ success: true, message: 'pong', timestamp: Date.now() });
      break;
    default:
      sendResponse({ error: '未知的操作类型' });
  }

  return true; // 保持消息通道开放
});

// 处理内容提取
async function handleContentExtraction(request, sender, sendResponse) {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    console.log('开始高级内容提取，页面URL:', tab.url);

    // 注入所有必要的函数
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: waitForDynamicContent
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: isContentNode
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: expandCollapsedContent
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: mightContainContent
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: removeUnwantedElements
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: removeHiddenElements
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: removeEmptyElements
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: findMainContentAdvanced
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: hasSignificantContent
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: findBestContentByAnalysis
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: calculateContentScore
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: filterBodyContent
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: isContentElement
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: extractStructuredContent
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: extractAndCleanText
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: analyzeContentAdvanced
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: detectContentType
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: extractMetadata
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: countWords
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: detectLanguage
    });

    // 最后执行主提取函数
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: extractPageContent
    });

    if (results && results[0] && results[0].result) {
      const extractedContent = results[0].result;
      console.log('高级内容提取完成:', {
        wordCount: extractedContent.wordCount,
        hasStructure: extractedContent.structure?.hasStructure,
        contentType: extractedContent.structure?.contentType,
        extractionMethod: extractedContent.extractionMethod
      });

      sendResponse({
        success: true,
        content: extractedContent,
        url: tab.url,
        title: tab.title
      });
    } else {
      console.error('内容提取结果为空');
      sendResponse({ error: '内容提取失败：未获取到有效内容' });
    }
  } catch (error) {
    console.error('内容提取错误:', error);
    sendResponse({ error: `内容提取失败: ${error.message}` });
  }
}

// 处理AI总结
async function handleSummarization(request, sender, sendResponse) {
  try {
    const config = await chrome.storage.sync.get(['apiConfig', 'promptTemplates', 'selectedTemplate']);
    const storedApiConfig = config.apiConfig;
    const template = config.promptTemplates[config.selectedTemplate] || config.promptTemplates.default;

    // 使用硬编码的API配置
    const apiConfig = {
      ...storedApiConfig,
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl
    };

    if (!apiConfig.apiKey) {
      sendResponse({ error: 'API密钥配置错误' });
      return;
    }

    // 清理内容，确保不包含可能导致编码问题的字符
    const cleanContent = request.content.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

    // 替换提示词中的变量
    const prompt = template.prompt.replace('{content}', cleanContent);

    // 调用AI API
    const summary = await callAIAPI(apiConfig, prompt);

    sendResponse({
      success: true,
      summary: summary,
      template: template.name
    });
  } catch (error) {
    console.error('AI总结错误:', error);
    sendResponse({ error: error.message });
  }
}

// 处理API配置测试
async function handleTestAPIConfig(sendResponse) {
  try {
    console.log('开始测试API配置...');

    const config = await chrome.storage.sync.get(['apiConfig']);
    const storedApiConfig = config.apiConfig;

    // 使用硬编码的API配置
    const apiConfig = {
      ...storedApiConfig,
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl
    };

    console.log('测试API配置:', {
      provider: apiConfig.provider,
      baseUrl: apiConfig.baseUrl,
      hasApiKey: !!apiConfig.apiKey,
      apiKeyPrefix: apiConfig.apiKey ? apiConfig.apiKey.substring(0, 10) + '...' : 'none',
      model: apiConfig.model
    });

    if (!apiConfig.apiKey) {
      sendResponse({ error: 'API密钥未配置' });
      return;
    }

    // 验证API配置
    const isValidConfig = await validateAPIConfig(apiConfig);

    if (isValidConfig) {
      sendResponse({
        success: true,
        message: 'API配置验证成功',
        config: {
          provider: apiConfig.provider,
          baseUrl: apiConfig.baseUrl,
          model: apiConfig.model,
          hasApiKey: !!apiConfig.apiKey
        }
      });
    } else {
      sendResponse({
        error: 'API配置验证失败',
        config: {
          provider: apiConfig.provider,
          baseUrl: apiConfig.baseUrl,
          model: apiConfig.model,
          hasApiKey: !!apiConfig.apiKey
        }
      });
    }
  } catch (error) {
    console.error('API配置测试错误:', error);
    sendResponse({ error: `API配置测试失败: ${error.message}` });
  }
}

// 处理Markdown提取
async function handleMarkdownExtraction(request, sender, sendResponse) {
  try {
    console.log('开始处理Markdown提取请求:', {
      contentLength: request.content ? request.content.length : 0,
      title: request.title,
      url: request.url
    });

    const config = await chrome.storage.sync.get(['apiConfig']);
    const storedApiConfig = config.apiConfig;
    console.log('获取存储的API配置:', storedApiConfig);

    // 使用硬编码的API配置
    const apiConfig = {
      ...storedApiConfig,
      apiKey: HARDCODED_API_CONFIG.apiKey,
      baseUrl: HARDCODED_API_CONFIG.baseUrl
    };

    console.log('最终API配置:', {
      provider: apiConfig.provider,
      baseUrl: apiConfig.baseUrl,
      hasApiKey: !!apiConfig.apiKey,
      apiKeyPrefix: apiConfig.apiKey ? apiConfig.apiKey.substring(0, 10) + '...' : 'none',
      model: apiConfig.model
    });

    if (!apiConfig.apiKey) {
      console.error('API密钥配置错误');
      sendResponse({ error: 'API密钥配置错误' });
      return;
    }

    // 验证API配置
    console.log('开始验证API配置...');
    const isValidConfig = await validateAPIConfig(apiConfig);
    if (!isValidConfig) {
      console.warn('API配置验证失败，但继续尝试请求');
    } else {
      console.log('API配置验证成功');
    }

    // 清理内容，确保不包含可能导致编码问题的字符
    const cleanContent = request.content.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

    // 使用新的Markdown处理器
    const processor = markdownProcessor;
    const chunks = processor.splitContentIntoChunks(cleanContent);

    if (chunks.length === 1) {
      // 单块处理
      const markdown = await processor.processChunk(
        apiConfig,
        chunks[0],
        request.title,
        request.url,
        1,
        1
      );

      const finalMarkdown = processor.addMarkdownHeader(markdown, request.title, request.url);

      sendResponse({
        success: true,
        markdown: finalMarkdown,
        chunks: 1
      });
    } else {
      // 分块处理
      const markdownChunks = [];

      for (let i = 0; i < chunks.length; i++) {
        try {
          const chunkMarkdown = await processor.processChunk(
            apiConfig,
            chunks[i],
            request.title,
            request.url,
            i + 1,
            chunks.length
          );
          markdownChunks.push(chunkMarkdown);

          console.log(`处理完成块 ${i + 1}/${chunks.length}`);
        } catch (chunkError) {
          console.error(`处理块 ${i + 1} 失败:`, chunkError);
          // 如果某个块失败，使用原始内容
          markdownChunks.push(`\n\n**[块 ${i + 1} 处理失败，显示原始内容]**\n\n${chunks[i]}\n\n`);
        }
      }

      // 合并所有块
      const finalMarkdown = processor.combineMarkdownChunks(markdownChunks, request.title, request.url);

      // 验证内容完整性
      const validation = processor.validateMarkdownContent(cleanContent, finalMarkdown);
      if (validation.warnings.length > 0) {
        console.warn('Markdown内容验证警告:', validation.warnings);
      }

      sendResponse({
        success: true,
        markdown: finalMarkdown,
        chunks: chunks.length,
        validation: validation
      });
    }
  } catch (error) {
    console.error('Markdown提取错误:', error);
    sendResponse({ error: formatErrorMessage(error) });
  }
}

// 获取配置
async function handleGetConfig(sendResponse) {
  try {
    const config = await chrome.storage.sync.get();
    sendResponse({ success: true, config: config });
  } catch (error) {
    sendResponse({ error: error.message });
  }
}

// 保存配置
async function handleSaveConfig(config, sendResponse) {
  try {
    await chrome.storage.sync.set(config);
    sendResponse({ success: true });
  } catch (error) {
    sendResponse({ error: error.message });
  }
}

// 调用AI API
async function callAIAPI(apiConfig, prompt) {
  const maxRetries = 3;
  let lastError;

  for (let i = 0; i < maxRetries; i++) {
    try {
      // 确保API密钥只包含ASCII字符
      const cleanApiKey = apiConfig.apiKey.replace(/[^\x00-\x7F]/g, "");

      const requestBody = {
        model: apiConfig.model || 'qwen-plus',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的内容总结助手，能够准确提取和总结各种类型文档的核心信息。请用中文回答。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: apiConfig.temperature || 0.7,
        max_tokens: apiConfig.maxTokens || 2000,
        top_p: 0.8
      };

      const response = await fetch(`${apiConfig.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${cleanApiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error && errorData.error.message) {
            errorMessage = errorData.error.message;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          if (errorText) {
            errorMessage += ` - ${errorText}`;
          }
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      // 处理不同的响应格式
      if (data.choices && data.choices.length > 0) {
        return data.choices[0].message.content;
      } else if (data.output && data.output.choices && data.output.choices.length > 0) {
        return data.output.choices[0].message.content;
      } else {
        throw new Error('API响应格式错误');
      }

    } catch (error) {
      lastError = error;
      console.error(`API调用失败 (尝试 ${i + 1}/${maxRetries}):`, error);

      // 如果不是最后一次尝试，等待后重试
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }

  throw lastError;
}

// 测试API连接
async function handleTestConnection(apiConfig, sendResponse) {
  try {
    const testPrompt = '请回复"连接测试成功"';
    const response = await callAIAPI(apiConfig, testPrompt);

    if (response && response.includes('连接测试成功')) {
      sendResponse({ success: true, message: 'API连接测试成功' });
    } else {
      sendResponse({ success: true, message: 'API连接正常，但响应内容异常', response });
    }
  } catch (error) {
    console.error('API连接测试失败:', error);
    sendResponse({ success: false, message: formatErrorMessage(error) });
  }
}

// 导出配置
async function handleExportConfig(sendResponse) {
  try {
    const config = await chrome.storage.sync.get();
    const exportData = {
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      config: {
        promptTemplates: config.promptTemplates,
        selectedTemplate: config.selectedTemplate,
        uiSettings: config.uiSettings
        // 不导出API配置和历史记录（安全考虑）
      }
    };

    sendResponse({ success: true, data: exportData });
  } catch (error) {
    console.error('导出配置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 导入配置
async function handleImportConfig(importData, sendResponse) {
  try {
    if (!importData || !importData.config) {
      throw new Error('导入数据格式错误');
    }

    const currentConfig = await chrome.storage.sync.get();
    const newConfig = {
      ...currentConfig,
      ...importData.config
    };

    await chrome.storage.sync.set(newConfig);
    sendResponse({ success: true });
  } catch (error) {
    console.error('导入配置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 清空历史记录
async function handleClearHistory(sendResponse) {
  try {
    await chrome.storage.local.set({ history: [] });
    sendResponse({ success: true });
  } catch (error) {
    console.error('清空历史记录失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 重置所有设置
async function handleResetSettings(sendResponse) {
  try {
    await chrome.storage.sync.clear();

    // 重新设置默认配置
    const defaultConfig = {
      apiConfig: {
        provider: 'qwen',
        apiKey: HARDCODED_API_CONFIG.apiKey,
        baseUrl: HARDCODED_API_CONFIG.baseUrl,
        model: 'qwen-plus'
      },
      promptTemplates: {
        default: {
          name: '默认总结',
          prompt: '请对以下内容进行总结，提取关键信息和要点：\n\n{content}'
        },
        news: {
          name: '新闻总结',
          prompt: '请总结这篇新闻的主要内容，包括：\n1. 核心事件\n2. 关键人物\n3. 时间地点\n4. 影响和意义\n\n内容：{content}'
        },
        academic: {
          name: '学术文章',
          prompt: '请总结这篇学术文章，包括：\n1. 研究目的和问题\n2. 主要方法\n3. 核心发现\n4. 结论和意义\n\n内容：{content}'
        },
        technical: {
          name: '技术文档',
          prompt: '请总结这份技术文档的要点：\n1. 主要功能特性\n2. 使用方法\n3. 注意事项\n4. 适用场景\n\n内容：{content}'
        }
      },
      selectedTemplate: 'default',
      uiSettings: {
        theme: 'dark',
        sidebarWidth: 380,
        autoSummarize: false
      },
      history: []
    };

    await chrome.storage.sync.set(defaultConfig);
    sendResponse({ success: true });
  } catch (error) {
    console.error('重置设置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 添加历史记录（使用local storage）
async function handleAddHistoryRecord(record, sendResponse) {
  try {
    // 检查记录大小
    const recordSize = JSON.stringify(record).length;
    if (recordSize > 7000) {
      console.warn('历史记录过大，进行压缩处理');
      record = compressHistoryRecord(record);
    }

    const { history = [] } = await chrome.storage.local.get(['history']);

    // 添加新记录到历史记录开头
    const newRecord = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      ...record
    };
    history.unshift(newRecord);

    // 限制历史记录数量（最多保存50条）
    if (history.length > 50) {
      history.splice(50);
    }

    await chrome.storage.local.set({ history });
    sendResponse({ success: true });
  } catch (error) {
    console.error('添加历史记录失败:', error);

    // 如果是配额错误，尝试清理
    if (error.message.includes('quota') || error.message.includes('QUOTA_BYTES')) {
      try {
        await cleanupHistoryStorage();
        // 重试保存压缩后的记录
        const compressedRecord = compressHistoryRecord(record);
        const { history = [] } = await chrome.storage.local.get(['history']);
        history.unshift({
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          ...compressedRecord
        });
        if (history.length > 20) {
          history.splice(20);
        }
        await chrome.storage.local.set({ history });
        sendResponse({ success: true, warning: '存储空间不足，已压缩保存' });
      } catch (retryError) {
        sendResponse({ success: false, error: '存储空间不足，请清理历史记录' });
      }
    } else {
      sendResponse({ success: false, error: error.message });
    }
  }
}

// 压缩历史记录
function compressHistoryRecord(record) {
  const compressed = { ...record };

  if (compressed.content && compressed.content.length > 3000) {
    compressed.content = compressed.content.substring(0, 3000) + '...[已截断]';
  }

  if (compressed.summary && compressed.summary.length > 1000) {
    compressed.summary = compressed.summary.substring(0, 1000) + '...[已截断]';
  }

  // 移除不必要的字段
  delete compressed.rawContent;
  delete compressed.metadata;

  return compressed;
}

// 清理历史记录存储
async function cleanupHistoryStorage() {
  try {
    const { history = [] } = await chrome.storage.local.get(['history']);
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

    const recentHistory = history
      .filter(record => {
        const recordTime = new Date(record.timestamp).getTime();
        return recordTime > oneWeekAgo;
      })
      .slice(0, 20)
      .map(record => compressHistoryRecord(record));

    await chrome.storage.local.set({ history: recentHistory });
    console.log(`历史记录清理完成，保留 ${recentHistory.length} 条记录`);
  } catch (error) {
    console.error('清理历史记录失败:', error);
  }
}

// 格式化错误信息
function formatErrorMessage(error) {
  const message = error.message || '未知错误';

  if (message.includes('401')) {
    return 'API密钥无效或已过期，请检查配置或联系管理员';
  } else if (message.includes('403')) {
    return 'API访问被拒绝，请检查权限';
  } else if (message.includes('429')) {
    return 'API调用频率超限，请稍后重试';
  } else if (message.includes('500')) {
    return 'API服务器错误，请稍后重试';
  } else if (message.includes('network') || message.includes('fetch')) {
    return '网络连接错误，请检查网络';
  } else if (message.includes('quota') || message.includes('QUOTA_BYTES')) {
    return '存储空间不足，请清理历史记录或减少内容长度';
  } else if (message.includes('Could not establish connection')) {
    return '扩展连接错误，请刷新页面后重试';
  } else {
    return message;
  }
}

// 提取内容为Markdown格式
async function extractToMarkdown(apiConfig, content, title, url, chunkIndex = null, totalChunks = null) {
  const chunkInfo = chunkIndex ? `（第${chunkIndex}/${totalChunks}部分）` : '';

  // 清理内容，确保不包含可能导致编码问题的字符
  const cleanContent = content.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
  const cleanTitle = title.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

  const prompt = `请将以下网页内容转换为标准的Markdown格式。要求：

1. 保持原文的结构层次，使用合适的标题级别（# ## ### 等）
2. 保留重要的文本格式（粗体、斜体、链接等）
3. 将列表转换为Markdown列表格式
4. 保留代码块和引用块的格式
5. 移除广告、导航菜单等无关内容
6. 确保输出的Markdown格式规范且易读
7. 如果有图片，保留图片的alt文本信息

网页标题：${cleanTitle}${chunkInfo}
网页链接：${url}

网页内容：
${cleanContent}

请直接输出转换后的Markdown内容，不要添加额外的说明文字：`;

  return await callAIAPI(apiConfig, prompt);
}

// 将内容分割成块
function splitContentIntoChunks(content, maxChunkSize) {
  const chunks = [];
  const paragraphs = content.split('\n\n');
  let currentChunk = '';

  for (const paragraph of paragraphs) {
    // 如果单个段落就超过限制，需要进一步分割
    if (paragraph.length > maxChunkSize) {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = '';
      }

      // 按句子分割长段落
      const sentences = paragraph.split(/[。！？.!?]/);
      let currentSentenceGroup = '';

      for (const sentence of sentences) {
        if (currentSentenceGroup.length + sentence.length > maxChunkSize) {
          if (currentSentenceGroup) {
            chunks.push(currentSentenceGroup.trim());
          }
          currentSentenceGroup = sentence;
        } else {
          currentSentenceGroup += sentence;
        }
      }

      if (currentSentenceGroup) {
        currentChunk = currentSentenceGroup;
      }
    } else {
      // 检查添加这个段落是否会超过限制
      if (currentChunk.length + paragraph.length > maxChunkSize) {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
        }
        currentChunk = paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// 合并Markdown块
function combineMarkdownChunks(markdownChunks, title, url) {
  const header = `# ${title}

> 来源：${url}
> 生成时间：${new Date().toLocaleString('zh-CN')}

---

`;

  const combinedContent = markdownChunks.join('\n\n---\n\n');

  return header + combinedContent;
}

// 页面内容提取函数（将在页面上下文中执行）
async function extractPageContent() {
  // 等待动态内容加载
  await waitForDynamicContent();

  // 创建页面副本进行处理
  const pageClone = document.cloneNode(true);

  // 展开折叠内容
  expandCollapsedContent(pageClone);

  // 移除不需要的元素
  removeUnwantedElements(pageClone);

  // 智能查找主要内容区域
  const mainContent = findMainContentAdvanced(pageClone);

  // 提取结构化内容
  const structuredContent = extractStructuredContent(mainContent);

  // 提取文本内容
  let textContent = extractAndCleanText(mainContent);

  // 分析内容
  const contentAnalysis = analyzeContentAdvanced(mainContent);

  // 提取元数据
  const metadata = extractMetadata();

  return {
    content: textContent,
    structuredContent: structuredContent,
    title: document.title,
    url: window.location.href,
    wordCount: countWords(textContent),
    language: detectLanguage(textContent),
    structure: contentAnalysis,
    metadata: metadata,
    extractedAt: new Date().toISOString(),
    extractionMethod: 'advanced'
  };
}

// 等待动态内容加载
async function waitForDynamicContent() {
  return new Promise((resolve) => {
    let timeoutId;
    let observer;
    let resolved = false;
    const dynamicContentTimeout = 5000; // 5秒
    const observerTimeout = 3000; // 3秒

    const resolveOnce = () => {
      if (resolved) return;
      resolved = true;

      if (observer) observer.disconnect();
      if (timeoutId) clearTimeout(timeoutId);
      resolve();
    };

    // 设置超时
    timeoutId = setTimeout(resolveOnce, dynamicContentTimeout);

    // 观察DOM变化
    if (window.MutationObserver) {
      observer = new MutationObserver((mutations) => {
        const hasSignificantChanges = mutations.some(mutation => {
          return mutation.type === 'childList' &&
                 mutation.addedNodes.length > 0 &&
                 Array.from(mutation.addedNodes).some(node =>
                   node.nodeType === Node.ELEMENT_NODE &&
                   isContentNode(node)
                 );
        });

        if (hasSignificantChanges) {
          setTimeout(resolveOnce, observerTimeout);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // 如果页面已经稳定，立即解析
    if (document.readyState === 'complete') {
      setTimeout(resolveOnce, 500);
    }
  });
}

// 检查节点是否为内容节点
function isContentNode(node) {
  if (!node.tagName) return false;

  const contentTags = ['P', 'DIV', 'ARTICLE', 'SECTION', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'UL', 'OL', 'LI'];
  const hasContentTag = contentTags.includes(node.tagName);
  const hasText = node.textContent && node.textContent.trim().length > 20;

  return hasContentTag && hasText;
}

// 展开折叠内容
function expandCollapsedContent(doc) {
  // 展开details元素
  const details = doc.querySelectorAll('details');
  details.forEach(detail => {
    detail.open = true;
  });

  // 处理展开按钮
  const expandButtons = doc.querySelectorAll('[aria-expanded="false"], .expand, .show-more, .read-more');
  expandButtons.forEach(button => {
    if (button.hasAttribute('aria-expanded')) {
      button.setAttribute('aria-expanded', 'true');
    }
  });

  // 移除可能隐藏内容的样式
  const hiddenElements = doc.querySelectorAll('[style*="display: none"], [style*="display:none"]');
  hiddenElements.forEach(element => {
    if (mightContainContent(element)) {
      element.style.display = '';
    }
  });
}

// 判断元素是否可能包含内容
function mightContainContent(element) {
  const contentIndicators = ['content', 'text', 'article', 'post', 'story', 'body'];
  const className = (element.className || '').toLowerCase();
  const id = (element.id || '').toLowerCase();

  return contentIndicators.some(indicator =>
    className.includes(indicator) || id.includes(indicator)
  );
}

// 移除不需要的元素
function removeUnwantedElements(doc) {
  const unwantedSelectors = [
    // 脚本和样式
    'script', 'style', 'noscript',

    // 导航和菜单
    'nav', 'header', 'footer', 'aside',
    '.navigation', '.nav', '.menu', '.sidebar',
    '.breadcrumb', '.breadcrumbs',

    // 广告和推广
    '.advertisement', '.ads', '.ad', '.advert',
    '.sponsored', '.promotion', '.banner',
    '[class*="ad-"]', '[id*="ad-"]',
    '[class*="ads-"]', '[id*="ads-"]',

    // 社交和分享
    '.social', '.social-share', '.share-buttons',
    '.social-media', '.follow-us',

    // 评论和互动
    '.comments', '.comment', '.discussion',
    '.reviews', '.rating', '.feedback',

    // 弹窗和模态框
    '.popup', '.modal', '.overlay', '.lightbox',
    '.newsletter', '.subscription',

    // 相关内容和推荐
    '.related', '.recommended', '.suggestions',
    '.more-stories', '.you-might-like',

    // 工具栏和控件
    '.toolbar', '.controls', '.player-controls',
    '.video-controls', '.audio-controls',

    // 其他干扰元素
    '.cookie-notice', '.gdpr-notice',
    '.loading', '.spinner', '.placeholder',
    'iframe', 'embed', 'object'
  ];

  unwantedSelectors.forEach(selector => {
    try {
      const elements = doc.querySelectorAll(selector);
      elements.forEach(element => {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
    } catch (error) {
      console.warn(`移除元素失败: ${selector}`, error);
    }
  });

  // 移除隐藏元素
  removeHiddenElements(doc);

  // 移除空元素
  removeEmptyElements(doc);
}

// 移除隐藏元素
function removeHiddenElements(doc) {
  const allElements = doc.querySelectorAll('*');
  allElements.forEach(element => {
    try {
      const style = window.getComputedStyle(element);
      if (style.display === 'none' ||
          style.visibility === 'hidden' ||
          style.opacity === '0' ||
          element.hidden) {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      }
    } catch (error) {
      // 忽略样式获取错误
    }
  });
}

// 移除空元素
function removeEmptyElements(doc) {
  const emptyElements = doc.querySelectorAll('div, span, p, section, article');
  emptyElements.forEach(element => {
    if (element.textContent.trim() === '' &&
        element.children.length === 0) {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    }
  });
}

// 高级主要内容查找
function findMainContentAdvanced(doc) {
  // 第一阶段：尝试语义化和常见选择器
  const primarySelectors = [
    'main', 'article', '[role="main"]', '[role="article"]',
    '.content', '.main-content', '.post-content', '.article-content',
    '.entry-content', '.page-content'
  ];

  for (const selector of primarySelectors) {
    const element = doc.querySelector(selector);
    if (element && hasSignificantContent(element)) {
      return element;
    }
  }

  // 第二阶段：更多特定选择器
  const secondarySelectors = [
    '.story-body', '.article-body', '.post-body', '.news-content',
    '.blog-post', '.post', '.entry', '.text-content',
    '#content', '#main', '#main-content', '#article', '#post'
  ];

  for (const selector of secondarySelectors) {
    const element = doc.querySelector(selector);
    if (element && hasSignificantContent(element)) {
      return element;
    }
  }

  // 第三阶段：智能分析
  const bestContent = findBestContentByAnalysis(doc);
  if (bestContent) {
    return bestContent;
  }

  // 最后使用body
  return filterBodyContent(doc.body || doc.documentElement);
}

// 检查元素是否包含有意义的内容
function hasSignificantContent(element) {
  const text = element.textContent || '';
  const wordCount = countWords(text);
  return wordCount > 50;
}

// 通过分析找到最佳内容区域
function findBestContentByAnalysis(doc) {
  const candidates = [];
  const potentialContainers = doc.querySelectorAll('div, section, article, main');

  potentialContainers.forEach(element => {
    const score = calculateContentScore(element);
    if (score > 0) {
      candidates.push({ element, score });
    }
  });

  candidates.sort((a, b) => b.score - a.score);
  return candidates.length > 0 ? candidates[0].element : null;
}

// 计算内容分数
function calculateContentScore(element) {
  let score = 0;

  const textLength = (element.textContent || '').length;
  score += Math.min(textLength / 100, 50);

  const paragraphs = element.querySelectorAll('p');
  score += Math.min(paragraphs.length * 2, 20);

  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  score += Math.min(headings.length * 3, 15);

  const className = (element.className || '').toLowerCase();
  const id = (element.id || '').toLowerCase();
  const contentKeywords = ['content', 'article', 'post', 'story', 'main', 'body'];

  contentKeywords.forEach(keyword => {
    if (className.includes(keyword) || id.includes(keyword)) {
      score += 5;
    }
  });

  const negativeKeywords = ['nav', 'sidebar', 'menu', 'header', 'footer', 'ad'];
  negativeKeywords.forEach(keyword => {
    if (className.includes(keyword) || id.includes(keyword)) {
      score -= 10;
    }
  });

  return score;
}

// 过滤body内容
function filterBodyContent(body) {
  const filteredContent = document.createElement('div');
  const contentElements = body.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, blockquote, pre');

  contentElements.forEach(element => {
    if (isContentElement(element)) {
      filteredContent.appendChild(element.cloneNode(true));
    }
  });

  return filteredContent;
}

// 判断是否为内容元素
function isContentElement(element) {
  const text = element.textContent || '';
  const wordCount = countWords(text);

  if (wordCount < 3) return false;
  if (element.tagName === 'A' && wordCount < 10) return false;

  const className = element.className || '';
  const id = element.id || '';
  const unwantedPatterns = ['menu', 'nav', 'header', 'footer', 'sidebar', 'ad'];

  for (const pattern of unwantedPatterns) {
    if (className.toLowerCase().includes(pattern) ||
        id.toLowerCase().includes(pattern)) {
      return false;
    }
  }

  return true;
}

// 提取结构化内容
function extractStructuredContent(element) {
  const structured = {
    headings: [],
    paragraphs: [],
    lists: [],
    tables: [],
    images: []
  };

  // 提取标题
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  headings.forEach(heading => {
    structured.headings.push({
      level: parseInt(heading.tagName.charAt(1)),
      text: heading.textContent.trim(),
      id: heading.id || null
    });
  });

  // 提取段落
  const paragraphs = element.querySelectorAll('p');
  paragraphs.forEach(p => {
    const text = p.textContent.trim();
    if (text.length > 10) {
      structured.paragraphs.push(text);
    }
  });

  // 提取列表
  const lists = element.querySelectorAll('ul, ol');
  lists.forEach(list => {
    const items = Array.from(list.querySelectorAll('li')).map(li => li.textContent.trim());
    if (items.length > 0) {
      structured.lists.push({
        type: list.tagName.toLowerCase(),
        items: items
      });
    }
  });

  // 提取表格
  const tables = element.querySelectorAll('table');
  tables.forEach(table => {
    const rows = Array.from(table.querySelectorAll('tr')).map(tr => {
      return Array.from(tr.querySelectorAll('td, th')).map(cell => cell.textContent.trim());
    });
    if (rows.length > 0) {
      structured.tables.push(rows);
    }
  });

  // 提取图片信息
  const images = element.querySelectorAll('img');
  images.forEach(img => {
    structured.images.push({
      src: img.src || '',
      alt: img.alt || '',
      title: img.title || ''
    });
  });

  return structured;
}

// 提取和清理文本
function extractAndCleanText(element) {
  let text = element.textContent || element.innerText || '';

  // 清理文本
  text = text
    .replace(/\s+/g, ' ')
    .replace(/\n\s*\n/g, '\n')
    .replace(/^\s+|\s+$/gm, '')
    .replace(/[^\w\s\u4e00-\u9fff.,!?;:()[\]{}""''—–-]/g, '')
    .trim();

  // 限制内容长度
  const maxLength = 12000;
  if (text.length > maxLength) {
    text = text.substring(0, maxLength) + '...';
  }

  return text;
}

// 高级内容分析
function analyzeContentAdvanced(element) {
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  const paragraphs = element.querySelectorAll('p');
  const lists = element.querySelectorAll('ul, ol');
  const images = element.querySelectorAll('img');
  const links = element.querySelectorAll('a');
  const tables = element.querySelectorAll('table');

  const totalText = element.textContent || '';
  const totalElements = element.querySelectorAll('*').length;
  const contentDensity = totalElements > 0 ? totalText.length / totalElements : 0;

  return {
    headingCount: headings.length,
    paragraphCount: paragraphs.length,
    listCount: lists.length,
    imageCount: images.length,
    linkCount: links.length,
    tableCount: tables.length,
    hasStructure: headings.length > 0,
    contentDensity: contentDensity,
    estimatedReadingTime: Math.ceil(countWords(totalText) / 200),
    contentType: detectContentType(element)
  };
}

// 检测内容类型
function detectContentType(element) {
  const text = element.textContent || '';
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  const codeBlocks = element.querySelectorAll('pre, code');
  const tables = element.querySelectorAll('table');

  if (codeBlocks.length > 3) return 'technical';
  if (tables.length > 2) return 'data';
  if (headings.length > 5) return 'article';
  if (text.length > 5000) return 'long-form';
  if (text.length < 500) return 'short-form';

  return 'general';
}

// 提取元数据
function extractMetadata() {
  const metadata = {};

  // 提取meta标签
  const metaTags = document.querySelectorAll('meta');
  metaTags.forEach(meta => {
    const name = meta.getAttribute('name') || meta.getAttribute('property');
    const content = meta.getAttribute('content');
    if (name && content) {
      metadata[name] = content;
    }
  });

  // 提取作者信息
  const authorMeta = document.querySelector('meta[name="author"]');
  if (authorMeta) {
    metadata.author = authorMeta.getAttribute('content');
  }

  // 提取发布日期
  const dateMeta = document.querySelector('meta[name="date"], meta[property="article:published_time"]');
  if (dateMeta) {
    metadata.publishDate = dateMeta.getAttribute('content');
  }

  return metadata;
}

// 统计词数
function countWords(text) {
  if (!text) return 0;

  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
  const englishWordCount = englishWords ? englishWords.length : 0;

  return chineseChars + englishWordCount;
}

// 检测语言
function detectLanguage(text) {
  if (!text) return 'unknown';

  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const totalChars = text.length;

  if (chineseChars / totalChars > 0.3) {
    return 'zh';
  } else {
    return 'en';
  }
}

// ==================== 钉钉认证相关处理函数 ====================

// 确保钉钉认证管理器已初始化
function ensureDingTalkAuthManager() {
  if (!dingTalkAuthManager) {
    dingTalkAuthManager = new DingTalkAuthManager();
    dingTalkErrorHandler = new DingTalkErrorHandler();
  }
  return dingTalkAuthManager;
}

// 获取钉钉认证状态
async function handleGetDingTalkAuthStatus(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const authStatus = authManager.getAuthStatus();

    sendResponse({
      success: true,
      data: authStatus
    });

  } catch (error) {
    console.error('获取钉钉认证状态失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 发起钉钉登录
async function handleInitiateDingTalkLogin(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const result = await authManager.initiateLogin();

    sendResponse({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('发起钉钉登录失败:', error);

    // 使用错误处理器处理错误
    if (dingTalkErrorHandler) {
      const errorResult = await dingTalkErrorHandler.handleAuthError(error, {
        traceId: `login_${Date.now()}`
      });

      sendResponse({
        success: false,
        error: error.message,
        errorInfo: errorResult.errorInfo
      });
    } else {
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }
}

// 选择钉钉组织
async function handleSelectDingTalkOrg(corpId, sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const selectedOrg = await authManager.selectOrganization(corpId);

    sendResponse({
      success: true,
      data: selectedOrg
    });

  } catch (error) {
    console.error('选择钉钉组织失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 钉钉登出
async function handleDingTalkLogout(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const result = await authManager.logout();

    sendResponse({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('钉钉登出失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 刷新钉钉认证状态
async function handleRefreshDingTalkAuth(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();

    // 重新验证Cookie并获取用户信息
    const cookieValid = await authManager.validateAuthCookie();
    if (cookieValid) {
      await authManager.fetchUserInfoAndSync();
    } else {
      await authManager.clearAuthState();
    }

    const authStatus = authManager.getAuthStatus();

    sendResponse({
      success: true,
      data: authStatus
    });

  } catch (error) {
    console.error('刷新钉钉认证状态失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 验证钉钉Cookie（调试用）
async function handleValidateDingTalkCookies(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const isValid = await authManager.validateAuthCookie();

    // 获取详细的Cookie信息
    const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
    const authCookieNames = [
      'account', 'login_aliyunid_ticket', '_tb_token_',
      'sessionid', 'sid', 'token', 'auth', 'login', 'user', 'session',
      'dingtalk_session', 'dt_session', 'dt_token', 'dt_auth'
    ];

    const authCookies = cookies.filter(c =>
      (authCookieNames.includes(c.name) ||
       c.name.includes('login') ||
       c.name.includes('auth') ||
       c.name.includes('session') ||
       c.name.includes('token')) && c.value
    );

    sendResponse({
      success: true,
      data: {
        isValid: isValid,
        totalCookies: cookies.length,
        authCookies: authCookies.length,
        cookieDetails: authCookies.map(c => ({
          name: c.name,
          hasValue: !!c.value,
          valueLength: c.value ? c.value.length : 0,
          domain: c.domain,
          expirationDate: c.expirationDate,
          isExpired: c.expirationDate && c.expirationDate * 1000 < Date.now()
        }))
      }
    });

  } catch (error) {
    console.error('验证钉钉Cookie失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 强制检查钉钉认证状态
async function handleForceDingTalkAuthCheck(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const traceId = `force_check_${Date.now()}`;

    console.log(`[${traceId}] 强制检查钉钉认证状态...`);

    // 检查Cookie状态
    const cookieValid = await authManager.validateAuthCookie();
    const currentAuthState = authManager.isAuthenticated;

    console.log(`[${traceId}] Cookie有效性: ${cookieValid}, 当前认证状态: ${currentAuthState}`);

    let actionTaken = 'none';

    // 如果Cookie有效但本地状态显示未认证，触发登录处理
    if (cookieValid && !currentAuthState) {
      console.log(`[${traceId}] 检测到Cookie有效但本地状态未认证，触发登录处理`);
      await authManager.handleUserLogin(traceId);
      actionTaken = 'triggered_login';
    }
    // 如果Cookie无效但本地状态显示已认证，触发登出处理
    else if (!cookieValid && currentAuthState) {
      console.log(`[${traceId}] 检测到Cookie无效但本地状态已认证，触发登出处理`);
      await authManager.handleUserLogout(traceId);
      actionTaken = 'triggered_logout';
    }

    // 获取最新的认证状态
    const authStatus = authManager.getAuthStatus();

    sendResponse({
      success: true,
      data: {
        authStatus: authStatus,
        cookieValid: cookieValid,
        actionTaken: actionTaken,
        traceId: traceId
      }
    });

  } catch (error) {
    console.error('强制检查钉钉认证状态失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 获取钉钉认证调试信息
async function handleGetDingTalkDebugInfo(sendResponse) {
  try {
    const authManager = ensureDingTalkAuthManager();
    const debugInfo = await authManager.getDebugInfo();

    sendResponse({
      success: true,
      data: debugInfo
    });

  } catch (error) {
    console.error('获取钉钉认证调试信息失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}
