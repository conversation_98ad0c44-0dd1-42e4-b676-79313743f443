<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome扩展测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-content {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007cba;
            margin: 20px 0;
        }
        .error-log {
            background: #ffe6e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>Chrome扩展功能测试</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <ol>
            <li>确保Chrome扩展已经加载并启用</li>
            <li>打开Chrome开发者工具（F12）查看控制台</li>
            <li>点击下面的按钮测试扩展功能</li>
            <li>检查侧边栏是否可以正常打开</li>
        </ol>
    </div>
    
    <div class="test-content">
        <h2>测试文章内容</h2>
        <p>这是一篇用于测试Chrome扩展Markdown提取功能的文章。文章包含了多个段落和不同的内容结构。</p>
        
        <h3>技术背景</h3>
        <p>人工智能技术在近年来取得了显著的进展，特别是在自然语言处理领域。大型语言模型如GPT、BERT等的出现，为文本理解和生成任务带来了革命性的改变。</p>
        
        <h3>应用场景</h3>
        <ul>
            <li>智能客服系统</li>
            <li>内容自动生成</li>
            <li>文档摘要提取</li>
            <li>多语言翻译</li>
        </ul>
        
        <h3>技术挑战</h3>
        <p>尽管AI技术发展迅速，但仍面临一些挑战：</p>
        <ol>
            <li>模型的可解释性问题</li>
            <li>数据隐私和安全考虑</li>
            <li>计算资源的高消耗</li>
            <li>模型偏见和公平性问题</li>
        </ol>
        
        <h3>未来展望</h3>
        <p>随着技术的不断进步，我们期待看到更加智能、高效和可靠的AI系统。这些系统将在教育、医疗、金融等各个领域发挥重要作用，为人类社会带来更多便利。</p>
        
        <blockquote>
            <p>"人工智能的发展不仅仅是技术的进步，更是人类认知能力的延伸。" - 某AI研究专家</p>
        </blockquote>
    </div>
    
    <div style="margin: 30px 0;">
        <button onclick="testExtensionConnection()">测试扩展连接</button>
        <button onclick="testMarkdownExtraction()">测试Markdown提取</button>
        <button onclick="openSidePanel()">打开侧边栏</button>
        <button onclick="checkExtensionStatus()">检查扩展状态</button>
    </div>
    
    <div id="testResults" class="error-log" style="display: none;"></div>
    
    <script>
        function log(message) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }
        
        function clearLog() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.textContent = '';
            resultsDiv.style.display = 'none';
        }
        
        function testExtensionConnection() {
            clearLog();
            log('开始测试扩展连接...');
            
            if (typeof chrome === 'undefined') {
                log('错误: Chrome API不可用');
                return;
            }
            
            if (!chrome.runtime) {
                log('错误: chrome.runtime不可用');
                return;
            }
            
            log('Chrome扩展API可用');
            log('扩展ID: ' + chrome.runtime.id);
            
            // 测试消息传递
            chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                if (chrome.runtime.lastError) {
                    log('错误: ' + chrome.runtime.lastError.message);
                } else {
                    log('扩展响应: ' + JSON.stringify(response));
                }
            });
        }
        
        function testMarkdownExtraction() {
            clearLog();
            log('开始测试Markdown提取...');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('错误: Chrome扩展API不可用');
                return;
            }
            
            const content = document.querySelector('.test-content').innerText;
            const title = document.title;
            const url = window.location.href;
            
            log('发送提取请求...');
            log('内容长度: ' + content.length + ' 字符');
            
            chrome.runtime.sendMessage({
                action: 'extractMarkdown',
                content: content,
                title: title,
                url: url
            }, (response) => {
                if (chrome.runtime.lastError) {
                    log('错误: ' + chrome.runtime.lastError.message);
                } else if (response.error) {
                    log('API错误: ' + response.error);
                } else {
                    log('提取成功!');
                    log('Markdown长度: ' + (response.markdown ? response.markdown.length : 0) + ' 字符');
                    if (response.chunks) {
                        log('处理块数: ' + response.chunks);
                    }
                }
            });
        }
        
        function openSidePanel() {
            clearLog();
            log('尝试打开侧边栏...');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('错误: Chrome扩展API不可用');
                return;
            }
            
            chrome.runtime.sendMessage({action: 'openSidePanel'}, (response) => {
                if (chrome.runtime.lastError) {
                    log('错误: ' + chrome.runtime.lastError.message);
                } else {
                    log('侧边栏请求已发送');
                }
            });
        }
        
        function checkExtensionStatus() {
            clearLog();
            log('检查扩展状态...');
            
            if (typeof chrome === 'undefined') {
                log('Chrome API: 不可用');
                return;
            }
            
            log('Chrome API: 可用');
            log('Runtime ID: ' + (chrome.runtime ? chrome.runtime.id : '不可用'));
            log('Storage API: ' + (chrome.storage ? '可用' : '不可用'));
            log('Tabs API: ' + (chrome.tabs ? '可用' : '不可用'));
            
            // 检查权限
            if (chrome.permissions) {
                chrome.permissions.getAll((permissions) => {
                    log('权限: ' + JSON.stringify(permissions, null, 2));
                });
            }
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('页面加载完成，自动检查扩展状态...');
                checkExtensionStatus();
            }, 1000);
        });
    </script>
</body>
</html>
