<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background: #ffe6e6;
            color: #d00;
        }
        .success {
            background: #e6ffe6;
            color: #080;
        }
    </style>
</head>
<body>
    <h1>Chrome扩展API测试</h1>
    
    <div class="test-section">
        <h2>1. 直接API测试</h2>
        <button onclick="testDirectAPI()">测试直接API调用</button>
        <div id="directResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Chrome扩展API测试</h2>
        <button onclick="testExtensionAPI()">测试扩展API调用</button>
        <div id="extensionResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 网络权限测试</h2>
        <button onclick="testNetworkPermissions()">测试网络权限</button>
        <div id="networkResult" class="result"></div>
    </div>

    <script>
        // 直接API测试
        async function testDirectAPI() {
            const resultDiv = document.getElementById('directResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer sk-466900693bb54313bb9c9a5feb986eb4'
                    },
                    body: JSON.stringify({
                        model: 'qwen-plus',
                        messages: [
                            { role: 'user', content: '测试API连接' }
                        ]
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `成功！响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    const errorText = await response.text();
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `失败！状态: ${response.status}, 错误: ${errorText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `网络错误: ${error.message}`;
            }
        }
        
        // Chrome扩展API测试
        async function testExtensionAPI() {
            const resultDiv = document.getElementById('extensionResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                // 检查是否在Chrome扩展环境中
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '错误: 不在Chrome扩展环境中';
                    return;
                }
                
                // 发送消息到background script
                chrome.runtime.sendMessage({
                    action: 'extractMarkdown',
                    content: '这是一个测试内容',
                    title: '测试页面',
                    url: window.location.href
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `扩展错误: ${chrome.runtime.lastError.message}`;
                    } else if (response.error) {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `API错误: ${response.error}`;
                    } else {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `成功！Markdown: ${response.markdown}`;
                    }
                });
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `扩展测试错误: ${error.message}`;
            }
        }
        
        // 网络权限测试
        async function testNetworkPermissions() {
            const resultDiv = document.getElementById('networkResult');
            resultDiv.textContent = '正在测试...';
            
            const testUrls = [
                'https://dashscope.aliyuncs.com',
                'https://www.baidu.com',
                'https://httpbin.org/get'
            ];
            
            const results = [];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    results.push(`${url}: ${response.status} ${response.statusText}`);
                } catch (error) {
                    results.push(`${url}: 错误 - ${error.message}`);
                }
            }
            
            resultDiv.className = 'result';
            resultDiv.textContent = results.join('\n');
        }
    </script>
</body>
</html>
