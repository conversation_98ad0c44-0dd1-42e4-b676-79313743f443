/**
 * Chrome插件内容提取功能测试脚本
 * 用于验证内容提取的完整性和准确性
 */

class ContentExtractionTester {
  constructor() {
    this.testResults = [];
    this.testCases = [
      {
        name: '基本文本提取测试',
        description: '验证基本段落文本的提取',
        expectedElements: ['h1', 'h2', 'p'],
        minWordCount: 100
      },
      {
        name: '结构化内容测试',
        description: '验证列表、表格等结构化内容的提取',
        expectedElements: ['ul', 'ol', 'table'],
        checkStructured: true
      },
      {
        name: '代码块提取测试',
        description: '验证代码块内容的提取',
        expectedElements: ['pre', 'code'],
        checkCodeBlocks: true
      },
      {
        name: '动态内容测试',
        description: '验证动态加载内容的提取',
        waitForDynamic: true,
        expectedKeywords: ['动态加载', '延迟加载']
      },
      {
        name: '内容过滤测试',
        description: '验证干扰元素的过滤效果',
        excludedKeywords: ['侧边栏', '广告内容', '页面底部', '模态框']
      }
    ];
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始内容提取测试...');
    
    for (const testCase of this.testCases) {
      try {
        const result = await this.runSingleTest(testCase);
        this.testResults.push(result);
        console.log(`${result.passed ? '✅' : '❌'} ${testCase.name}: ${result.message}`);
      } catch (error) {
        console.error(`❌ ${testCase.name} 测试失败:`, error);
        this.testResults.push({
          name: testCase.name,
          passed: false,
          message: `测试执行失败: ${error.message}`,
          error: error
        });
      }
    }

    this.generateTestReport();
  }

  // 运行单个测试
  async runSingleTest(testCase) {
    console.log(`🔍 运行测试: ${testCase.name}`);

    // 如果需要等待动态内容
    if (testCase.waitForDynamic) {
      await this.waitForDynamicContent();
    }

    // 模拟内容提取
    const extractedContent = await this.simulateContentExtraction();

    // 验证测试结果
    return this.validateTestResult(testCase, extractedContent);
  }

  // 等待动态内容加载
  async waitForDynamicContent() {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 10;
      const checkInterval = 500;

      const checkForContent = () => {
        attempts++;
        const dynamicContainer = document.getElementById('dynamic-container');
        const hasContent = dynamicContainer && 
                          dynamicContainer.querySelector('.dynamic-content');

        if (hasContent || attempts >= maxAttempts) {
          resolve();
        } else {
          setTimeout(checkForContent, checkInterval);
        }
      };

      checkForContent();
    });
  }

  // 模拟内容提取过程
  async simulateContentExtraction() {
    // 这里模拟实际的内容提取逻辑
    const mainContent = document.querySelector('main, article, .main-content');
    if (!mainContent) {
      throw new Error('未找到主要内容区域');
    }

    // 提取文本内容
    const textContent = mainContent.textContent || '';
    
    // 分析结构化内容
    const structuredContent = {
      headings: Array.from(mainContent.querySelectorAll('h1, h2, h3, h4, h5, h6'))
        .map(h => ({ level: parseInt(h.tagName.charAt(1)), text: h.textContent.trim() })),
      paragraphs: Array.from(mainContent.querySelectorAll('p'))
        .map(p => p.textContent.trim()).filter(text => text.length > 10),
      lists: Array.from(mainContent.querySelectorAll('ul, ol'))
        .map(list => ({
          type: list.tagName.toLowerCase(),
          items: Array.from(list.querySelectorAll('li')).map(li => li.textContent.trim())
        })),
      tables: Array.from(mainContent.querySelectorAll('table'))
        .map(table => Array.from(table.querySelectorAll('tr'))
          .map(tr => Array.from(tr.querySelectorAll('td, th'))
            .map(cell => cell.textContent.trim()))),
      codeBlocks: Array.from(mainContent.querySelectorAll('pre, code'))
        .map(code => code.textContent.trim()).filter(text => text.length > 5)
    };

    return {
      content: textContent,
      structuredContent: structuredContent,
      wordCount: this.countWords(textContent),
      hasStructure: structuredContent.headings.length > 0
    };
  }

  // 验证测试结果
  validateTestResult(testCase, extractedContent) {
    const result = {
      name: testCase.name,
      passed: true,
      message: '',
      details: {}
    };

    // 检查最小词数
    if (testCase.minWordCount) {
      const wordCount = extractedContent.wordCount;
      result.details.wordCount = wordCount;
      if (wordCount < testCase.minWordCount) {
        result.passed = false;
        result.message += `词数不足 (${wordCount} < ${testCase.minWordCount}); `;
      }
    }

    // 检查期望的元素
    if (testCase.expectedElements) {
      const missingElements = [];
      testCase.expectedElements.forEach(element => {
        const found = document.querySelector(`main ${element}, article ${element}`);
        if (!found) {
          missingElements.push(element);
        }
      });
      
      if (missingElements.length > 0) {
        result.passed = false;
        result.message += `缺少元素: ${missingElements.join(', ')}; `;
      }
    }

    // 检查结构化内容
    if (testCase.checkStructured) {
      const structured = extractedContent.structuredContent;
      result.details.structured = {
        headings: structured.headings.length,
        lists: structured.lists.length,
        tables: structured.tables.length
      };

      if (structured.lists.length === 0) {
        result.passed = false;
        result.message += '未提取到列表内容; ';
      }
      if (structured.tables.length === 0) {
        result.passed = false;
        result.message += '未提取到表格内容; ';
      }
    }

    // 检查代码块
    if (testCase.checkCodeBlocks) {
      const codeBlocks = extractedContent.structuredContent.codeBlocks;
      result.details.codeBlocks = codeBlocks.length;
      if (codeBlocks.length === 0) {
        result.passed = false;
        result.message += '未提取到代码块; ';
      }
    }

    // 检查期望的关键词
    if (testCase.expectedKeywords) {
      const content = extractedContent.content;
      const missingKeywords = testCase.expectedKeywords.filter(keyword => 
        !content.includes(keyword)
      );
      
      if (missingKeywords.length > 0) {
        result.passed = false;
        result.message += `缺少关键词: ${missingKeywords.join(', ')}; `;
      }
    }

    // 检查排除的关键词
    if (testCase.excludedKeywords) {
      const content = extractedContent.content;
      const foundExcluded = testCase.excludedKeywords.filter(keyword => 
        content.includes(keyword)
      );
      
      if (foundExcluded.length > 0) {
        result.passed = false;
        result.message += `包含应排除的内容: ${foundExcluded.join(', ')}; `;
      }
    }

    if (result.passed && !result.message) {
      result.message = '测试通过';
    }

    return result;
  }

  // 统计词数
  countWords(text) {
    if (!text) return 0;
    
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
    const englishWordCount = englishWords ? englishWords.length : 0;
    
    return chineseChars + englishWordCount;
  }

  // 生成测试报告
  generateTestReport() {
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const passRate = Math.round((passedTests / totalTests) * 100);

    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`通过率: ${passRate}%`);
    console.log('='.repeat(50));

    // 详细结果
    this.testResults.forEach(result => {
      console.log(`\n${result.passed ? '✅' : '❌'} ${result.name}`);
      console.log(`   ${result.message}`);
      if (result.details && Object.keys(result.details).length > 0) {
        console.log(`   详情:`, result.details);
      }
    });

    // 生成HTML报告
    this.generateHTMLReport();
  }

  // 生成HTML测试报告
  generateHTMLReport() {
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const passRate = Math.round((passedTests / totalTests) * 100);

    const reportHTML = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        <h2>内容提取测试报告</h2>
        <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h3>测试概览</h3>
          <p><strong>总测试数:</strong> ${totalTests}</p>
          <p><strong>通过测试:</strong> ${passedTests}</p>
          <p><strong>失败测试:</strong> ${totalTests - passedTests}</p>
          <p><strong>通过率:</strong> <span style="color: ${passRate >= 80 ? 'green' : passRate >= 60 ? 'orange' : 'red'}; font-weight: bold;">${passRate}%</span></p>
        </div>
        
        <h3>详细结果</h3>
        ${this.testResults.map(result => `
          <div style="border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: ${result.passed ? '#f0f8f0' : '#f8f0f0'};">
            <h4 style="margin: 0 0 10px 0; color: ${result.passed ? 'green' : 'red'};">
              ${result.passed ? '✅' : '❌'} ${result.name}
            </h4>
            <p style="margin: 5px 0;"><strong>结果:</strong> ${result.message}</p>
            ${result.details && Object.keys(result.details).length > 0 ? 
              `<p style="margin: 5px 0;"><strong>详情:</strong> ${JSON.stringify(result.details, null, 2)}</p>` : 
              ''
            }
          </div>
        `).join('')}
        
        <div style="margin-top: 20px; padding: 15px; background: #e6f3ff; border-radius: 5px;">
          <h4>测试建议</h4>
          ${passRate >= 80 ? 
            '<p>✅ 内容提取功能表现良好，建议继续保持。</p>' :
            passRate >= 60 ?
            '<p>⚠️ 内容提取功能基本可用，但仍有改进空间。</p>' :
            '<p>❌ 内容提取功能需要重大改进，建议重新检查算法。</p>'
          }
        </div>
      </div>
    `;

    // 将报告添加到页面
    const reportContainer = document.createElement('div');
    reportContainer.innerHTML = reportHTML;
    document.body.appendChild(reportContainer);
  }
}

// 自动运行测试（如果页面加载完成）
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const tester = new ContentExtractionTester();
      tester.runAllTests();
    }, 1000);
  });
} else {
  setTimeout(() => {
    const tester = new ContentExtractionTester();
    tester.runAllTests();
  }, 1000);
}

// 导出测试类供手动使用
window.ContentExtractionTester = ContentExtractionTester;
